import 'package:demarth/common/widgets/appbar/mobile_appbar.dart';
import 'package:demarth/features/shop/controllers/categories_controller.dart';
import 'package:demarth/home_menu.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../common/widgets/appbar/tabbar.dart';

import '../../../../utils/constants/colors.dart';
import '../../../../utils/helpers/helper_functions.dart';

import 'widgets/category_tab.dart';

class StoreScreen extends StatelessWidget {
  const StoreScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    // Check if this screen is rendered inside HomeMenu
    final isInHomeMenu =
        context.findAncestorWidgetOfExactType<HomeMenu>() != null;

    // Redirect to home if not on mobile
    if (screenWidth >= 600) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Get.offAll(const HomeMenu());
      });
      return const SizedBox.shrink();
    }

    final categories = CategoryController.instance.featuredCategories;
    final dark = THelperFunctions.isDarkMode(context);

    return PopScope(
      canPop: false,
      onPopInvoked: (value) async => Get.offAll(const HomeMenu()),
      child: DefaultTabController(
        length: categories.length,
        child: Scaffold(
          appBar: const TMobileAppBar(
            preferredSize: Size.fromHeight(kToolbarHeight),
            showSearchBar: true,
            showBackArrow: false,
          ),
          body: NestedScrollView(
            headerSliverBuilder: (_, innerBoxIsScrolled) {
              return [
                SliverAppBar(
                  pinned: true,
                  floating: true,
                  expandedHeight:
                      0, // Reduced height since we have mobile app bar
                  automaticallyImplyLeading: false,
                  backgroundColor: dark ? TColors.black : TColors.white,
                  bottom: TTabBar(
                      tabs: categories
                          .map((e) => Tab(child: Text(e.name)))
                          .toList()),
                )
              ];
            },
            body: TabBarView(
              children: categories
                  .map((category) => TCategoryTab(category: category))
                  .toList(),
            ),
          ),
          // Removed bottom navbar to prevent duplication
          bottomNavigationBar: null,
        ),
      ),
    );
  }
}
