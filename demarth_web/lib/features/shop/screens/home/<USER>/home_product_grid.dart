import 'package:demarth/common/widgets/products/product_cards/product_card_vertical.dart';
import 'package:demarth/common/widgets/shimmers/vertical_product_shimmer.dart';
import 'package:demarth/features/shop/controllers/product/product_controller.dart';
import 'package:demarth/features/shop/models/product_model.dart';
import 'package:demarth/utils/constants/sizes.dart';
import 'package:demarth/utils/constants/colors.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class HomeProductGrid extends StatefulWidget {
  const HomeProductGrid({
    super.key,
    required this.controller,
  });

  final ProductController controller;

  @override
  State<HomeProductGrid> createState() => _HomeProductGridState();
}

class _HomeProductGridState extends State<HomeProductGrid>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final RxInt selectedTabIndex = 0.obs;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _tabController.addListener(() {
      selectedTabIndex.value = _tabController.index;
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (widget.controller.isLoading.value) {
        return const TVerticalProductShimmer();
      }

      // Combine all product sources into one list
      List<ProductModel> allProducts = [];
      allProducts.addAll(widget.controller.allProducts);

      for (var product in widget.controller.filteredProducts) {
        if (!allProducts.any((p) => p.id == product.id)) {
          allProducts.add(product);
        }
      }

      for (var product in widget.controller.shopProducts) {
        if (!allProducts.any((p) => p.id == product.id)) {
          allProducts.add(product);
        }
      }

      if (allProducts.isEmpty) {
        return Center(
          child: Padding(
            padding:
                const EdgeInsets.symmetric(vertical: TSizes.spaceBtwSections),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.shopping_bag_outlined,
                    size: 50, color: Colors.grey),
                const SizedBox(height: TSizes.spaceBtwItems),
                Text(
                  'No Products Found!',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: TSizes.spaceBtwItems / 2),
                Text(
                  'We couldn\'t find any products at this time.',
                  style: Theme.of(context).textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Tab Bar
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(TSizes.cardRadiusMd),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 0),
                ),
              ],
            ),
            child: TabBar(
              controller: _tabController,
              isScrollable: false,
              indicatorSize: TabBarIndicatorSize.tab,
              indicator: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(TSizes.cardRadiusMd),
              ),
              labelColor: TColors.primary,
              unselectedLabelColor: Colors.grey,
              labelStyle: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
              unselectedLabelStyle: const TextStyle(
                fontWeight: FontWeight.normal,
                fontSize: 14,
              ),
              tabs: const [
                Tab(text: 'Explore'),
                Tab(text: 'Trending'),
                Tab(text: 'Exclusive'),
                Tab(text: "Today's Deal"),
              ],
            ),
          ),
          const SizedBox(height: TSizes.spaceBtwItems),

          // Tab Content
          SizedBox(
            height: 600, // Fixed height for the tab content
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildProductGrid(_getExploreProducts(allProducts)),
                _buildProductGrid(_getTrendingProducts(allProducts)),
                _buildProductGrid(_getExclusiveProducts(allProducts)),
                _buildProductGrid(_getTodaysDealsProducts(allProducts)),
              ],
            ),
          ),
        ],
      );
    });
  }

  /// Get products for Explore tab (all featured products)
  List<ProductModel> _getExploreProducts(List<ProductModel> allProducts) {
    return allProducts.where((product) => product.isFeatured == true).toList();
  }

  /// Get products for Trending tab (highest sold quantity and rating)
  List<ProductModel> _getTrendingProducts(List<ProductModel> allProducts) {
    var trending = allProducts
        .where((product) => product.soldQuantity > 0 || product.ratingCount > 0)
        .toList();

    // Sort by combination of sold quantity and rating count
    trending.sort((a, b) {
      final scoreA = (a.soldQuantity * 0.7) + (a.ratingCount * 0.3);
      final scoreB = (b.soldQuantity * 0.7) + (b.ratingCount * 0.3);
      return scoreB.compareTo(scoreA);
    });

    return trending.take(20).toList();
  }

  /// Get products for Exclusive tab (products with high ratings)
  List<ProductModel> _getExclusiveProducts(List<ProductModel> allProducts) {
    return allProducts
        .where((product) =>
            product.averageRating >= 4.0 && product.ratingCount >= 5)
        .toList()
      ..sort((a, b) => b.averageRating.compareTo(a.averageRating));
  }

  /// Get products for Today's Deal tab (products with sale prices)
  List<ProductModel> _getTodaysDealsProducts(List<ProductModel> allProducts) {
    var dealsProducts = allProducts
        .where((product) =>
            product.salePrice > 0 && product.salePrice < product.price)
        .toList();

    // Sort by discount percentage (highest discount first)
    dealsProducts.sort((a, b) {
      final discountA = ((a.price - a.salePrice) / a.price) * 100;
      final discountB = ((b.price - b.salePrice) / b.price) * 100;
      return discountB.compareTo(discountA);
    });

    return dealsProducts;
  }

  /// Calculate responsive main axis extent (card height)
  double _calculateMainAxisExtent(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return 184; // Mobile height
    }
    if (screenWidth < 900) return 200; // Tablet
    return 240; // Desktop
  }

  /// Calculate responsive cross axis count (columns)
  int _calculateCrossAxisCount(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) return 3; // Mobile: 3 products per row
    if (screenWidth < 900) return 4; // Tablet: 4 products per row
    if (screenWidth < 1200) return 5; // Medium: 5 products per row
    return 6; // Desktop: 6 products per row
  }

  /// Build product grid
  Widget _buildProductGrid(List<ProductModel> products) {
    if (products.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(TSizes.defaultSpace),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.inventory_2_outlined,
                size: 48,
                color: Colors.grey.shade400,
              ),
              const SizedBox(height: TSizes.sm),
              Text(
                'No products available',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey.shade600,
                    ),
              ),
            ],
          ),
        ),
      );
    }

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(horizontal: TSizes.defaultSpace),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: _calculateCrossAxisCount(context),
        mainAxisSpacing: TSizes.gridViewSpacing,
        crossAxisSpacing: TSizes.gridViewSpacing,
        mainAxisExtent: _calculateMainAxisExtent(context),
      ),
      itemCount: products.length > 8 ? 8 : products.length,
      itemBuilder: (_, index) => TProductCardVertical(
        product: products[index],
        isNetworkImage: true,
        isHomeScreen: true,
      ),
    );
  }
}
