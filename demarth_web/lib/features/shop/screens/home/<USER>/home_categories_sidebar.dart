import 'dart:async';
import 'package:demarth/common/widgets/shimmers/category_shimmer.dart';
import 'package:demarth/features/shop/controllers/categories_controller.dart';
import 'package:demarth/features/shop/controllers/product/product_controller.dart';
import 'package:demarth/features/shop/models/product_model.dart';

import 'package:demarth/utils/constants/colors.dart';
import 'package:demarth/utils/constants/sizes.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class HomeCategoriesSidebar extends StatefulWidget {
  final bool dark;

  const HomeCategoriesSidebar({
    super.key,
    required this.dark,
  });

  @override
  State<HomeCategoriesSidebar> createState() => _HomeCategoriesSidebarState();
}

class _HomeCategoriesSidebarState extends State<HomeCategoriesSidebar> {
  bool showAllCategories = false;

  @override
  Widget build(BuildContext context) {
    final categoryController = Get.put(CategoryController());

    return Card(
      elevation: 2,
      color: widget.dark ? TColors.dark : Colors.white,
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: TColors.primary.withOpacity(0.1)),
        ),
        child: Padding(
          padding: const EdgeInsets.all(TSizes.defaultSpace),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: TColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  'Featured Categories',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                        color: TColors.primary,
                      ),
                ),
              ),
              const SizedBox(height: TSizes.spaceBtwItems),
              Obx(() {
                if (categoryController.isLoading.value) {
                  return const TCategoryShimmer();
                }

                final featuredCategories = categoryController.allCategories
                    .where((category) => category.isFeatured)
                    .toList();

                final displayCategories = showAllCategories
                    ? featuredCategories
                    : featuredCategories.take(8).toList();

                return Column(
                  children: [
                    ListView.separated(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: displayCategories.length,
                      separatorBuilder: (_, __) =>
                          const SizedBox(height: TSizes.spaceBtwItems / 8),
                      itemBuilder: (_, index) {
                        final category = displayCategories[index];
                        return _CategoryItem(
                          name: category.name,
                          icon: _getCategoryIcon(category.name),
                          categoryId: category.id,
                        );
                      },
                    ),
                    if (featuredCategories.length > 8) ...[
                      const SizedBox(height: TSizes.spaceBtwItems),
                      InkWell(
                        onTap: () {
                          setState(() {
                            showAllCategories = !showAllCategories;
                          });
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              vertical: 8, horizontal: 12),
                          decoration: BoxDecoration(
                            color: TColors.primary.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                showAllCategories ? Icons.remove : Icons.add,
                                size: 16,
                                color: TColors.primary,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                showAllCategories
                                    ? 'Show Less'
                                    : 'More Categories',
                                style: Theme.of(context)
                                    .textTheme
                                    .labelMedium
                                    ?.copyWith(
                                      color: TColors.primary,
                                      fontWeight: FontWeight.w600,
                                    ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ],
                );
              }),
            ],
          ),
        ),
      ),
    );
  }

  // This function needs to be moved or passed as a parameter if it's not part of the new widget's scope
  String _getCategoryIcon(String categoryName) {
    // Implement your logic to return the correct icon path based on categoryName
    // For example:
    // if (categoryName == 'Electronics') return TImages.electronicsIcon;
    // else if (categoryName == 'Clothes') return TImages.clothesIcon;
    // else return TImages.defaultIcon;
    // For now, returning a placeholder image path to prevent the error
    return 'assets/icons/categories/sport_icon.png'; // Placeholder
  }
}

class _CategoryItem extends StatefulWidget {
  final String name;
  final String icon;
  final String categoryId;

  const _CategoryItem({
    required this.name,
    required this.icon,
    required this.categoryId,
  });

  @override
  State<_CategoryItem> createState() => _CategoryItemState();
}

class _CategoryItemState extends State<_CategoryItem> {
  bool _isHovering = false;
  final _productController = Get.put(ProductController());
  final RxList<ProductModel> _categoryProducts = <ProductModel>[].obs;
  final RxBool _isLoadingProducts = false.obs;
  Timer? _hoverTimer;

  @override
  void initState() {
    super.initState();
    // Pre-fetch products if needed, or fetch on hover
  }

  Future<void> _loadCategoryProducts() async {
    _isLoadingProducts.value = true;
    try {
      // Filter products from the already loaded allProducts list
      final filteredProducts = _productController.allProducts
          .where((product) => product.categoryId == widget.categoryId)
          .toList();

      _categoryProducts.assignAll(filteredProducts);
    } catch (e) {
      _categoryProducts.clear();
    } finally {
      _isLoadingProducts.value = false;
    }
  }

  OverlayEntry? _overlayEntry;

  void _onHover(bool isHovering) {
    setState(() {
      _isHovering = isHovering;
    });

    // Cancel any existing timer
    _hoverTimer?.cancel();

    if (_isHovering) {
      // Add a small delay before showing the overlay to prevent flickering
      _hoverTimer = Timer(const Duration(milliseconds: 300), () {
        if (_isHovering && mounted) {
          _loadCategoryProducts();
          _showProductOverlay(context);
        }
      });
    } else {
      // Hide immediately when not hovering
      _hideProductOverlay();
    }
  }

  void _showProductOverlay(BuildContext context) {
    if (_overlayEntry != null) return; // Already showing

    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;
    final offset = renderBox.localToGlobal(Offset.zero);

    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        left: offset.dx + size.width + 10, // Position to the right
        top: offset.dy,
        child: Material(
          elevation: 8,
          borderRadius: BorderRadius.circular(8),
          child: Container(
            width: 300,
            height: 400,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 10,
                  spreadRadius: 2,
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Products in ${widget.name}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 12),
                Expanded(
                  child: Obx(() {
                    if (_isLoadingProducts.value) {
                      return const Center(child: CircularProgressIndicator());
                    } else if (_categoryProducts.isEmpty) {
                      return const Center(
                        child: Text('No products found for this category.'),
                      );
                    } else {
                      return ListView.builder(
                        itemCount: _categoryProducts.length
                            .clamp(0, 5), // Show max 5 products
                        itemBuilder: (context, index) {
                          final product = _categoryProducts[index];
                          return Container(
                            margin: const EdgeInsets.only(bottom: 8),
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey.shade200),
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Row(
                              children: [
                                // Product thumbnail
                                Container(
                                  width: 40,
                                  height: 40,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(4),
                                    color: Colors.grey.shade100,
                                  ),
                                  child: product.thumbnail.isNotEmpty
                                      ? ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(4),
                                          child: Image.network(
                                            product.thumbnail,
                                            fit: BoxFit.cover,
                                            errorBuilder: (context, error,
                                                    stackTrace) =>
                                                Icon(Icons.image,
                                                    color:
                                                        Colors.grey.shade400),
                                          ),
                                        )
                                      : Icon(Icons.image,
                                          color: Colors.grey.shade400),
                                ),
                                const SizedBox(width: 12),
                                // Product details
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        product.title,
                                        style: const TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.w500,
                                        ),
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        '\$${product.price.toStringAsFixed(2)}',
                                        style: TextStyle(
                                          fontSize: 11,
                                          color: Colors.green.shade600,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      );
                    }
                  }),
                ),
                if (_categoryProducts.length > 5)
                  Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: Text(
                      'and ${_categoryProducts.length - 5} more products...',
                      style: TextStyle(
                        fontSize: 11,
                        color: Colors.grey.shade600,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void _hideProductOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  @override
  void dispose() {
    _hoverTimer?.cancel();
    _hideProductOverlay();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => _onHover(true),
      onExit: (_) => _onHover(false),
      child: ListTile(
        leading: SizedBox(
          width: 24,
          height: 24,
          child: Image.asset(widget.icon, fit: BoxFit.contain),
        ),
        title: Text(widget.name),
        onTap: () {
          // Handle category tap, e.g., navigate to category products
        },
      ),
    );
  }
}
