import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:readmore/readmore.dart';

import 'package:demarth/data/repositories/authentication/authentication_repository.dart';
import 'package:demarth/data/repositories/shop/shop_repository.dart';
import 'package:demarth/features/shop/controllers/product/cart_controller.dart';
import 'package:demarth/features/shop/controllers/product/product_controller.dart';
import 'package:demarth/features/shop/controllers/product_review_controller.dart';
import 'package:demarth/features/shop/models/product_model.dart';
import 'package:demarth/features/shop/models/shop_model.dart';

import 'package:demarth/features/shop/screens/product_detail/widgets/rating_share_widget.dart';
import 'package:demarth/features/shop/screens/product_detail/widgets/product_meta_data.dart';
import 'package:demarth/features/shop/screens/product_detail/widgets/product_attributes.dart';
import 'package:demarth/features/shop/screens/product_detail/widgets/product_detail_image_slider.dart';

import '../../../../common/widgets/custom_shapes/containers/web_navbar.dart';
import '../../../../common/widgets/layouts/base_layout.dart';
import '../../../../common/widgets/layouts/grid_layout.dart';
import '../../../../common/widgets/products/cart/bottom_add_to_cart_widget.dart';
import '../../../../common/widgets/products/product_cards/product_card_vertical.dart';
import '../../../../utils/constants/colors.dart';
import '../../../../utils/constants/sizes.dart';

class ProductDetailScreen extends StatelessWidget {
  const ProductDetailScreen({
    super.key,
    this.product,
    this.productId,
  });

  final ProductModel? product;
  final String? productId;

  static String generateSlug(String name) {
    return name
        .toLowerCase()
        .replaceAll(' ', '-')
        .replaceAll(RegExp(r'[^\w-]'), '');
  }

  static String? extractProductId(String slug) {
    final parts = slug.split('-');
    return parts.isNotEmpty ? parts.last : null;
  }

  @override
  Widget build(BuildContext context) {
    final cartController = CartController.instance;
    final reviewController = Get.put(ProductReviewController());

    if (!Get.isRegistered<ShopRepository>()) {
      Get.put(ShopRepository());
    }

    if (product == null && productId != null) {
      return FutureBuilder<ProductModel?>(
        future: ProductController.instance.getProductById(productId!),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError || !snapshot.hasData) {
            return Center(
              child: Text('Product not found',
                  style: Theme.of(context).textTheme.headlineSmall),
            );
          }
          return _ProductDetailContent(
            product: snapshot.data!,
            reviewController: reviewController,
            cartController: cartController,
          );
        },
      );
    }

    if (product == null) {
      return Center(
        child: Text('Product not found',
            style: Theme.of(context).textTheme.headlineSmall),
      );
    }

    reviewController.loadProductReviews(product!.id);

    return _ProductDetailContent(
      product: product!,
      reviewController: reviewController,
      cartController: cartController,
    );
  }
}

class _ProductDetailContent extends StatelessWidget {
  const _ProductDetailContent({
    required this.product,
    required this.reviewController,
    required this.cartController,
  });

  final ProductModel product;
  final ProductReviewController reviewController;
  final CartController cartController;

  void _navigateToShop(BuildContext context, ShopModel shop) {
    if (product.shopId == null || product.shopId!.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Shop information is incomplete')),
      );
      return;
    }
    context.go('/shop/${product.shopId}', extra: shop);
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 768;

    return Scaffold(
      body: TBaseLayout(
        appBar: isMobile
            ? null
            : const PreferredSize(
                preferredSize: Size.fromHeight(160),
                child: TWebNavBar(),
              ),
        child: Center(
          child: ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 1200),
            child: Padding(
              padding: const EdgeInsets.all(TSizes.defaultSpace),
              child: LayoutBuilder(
                builder: (context, constraints) {
                  final isDesktop = constraints.maxWidth > 768;

                  if (isDesktop) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              flex: 5,
                              child: Column(
                                children: [
                                  TProductImageSlider(product: product),
                                  const SizedBox(height: TSizes.spaceBtwItems),
                                ],
                              ),
                            ),
                            const SizedBox(width: TSizes.spaceBtwItems),
                            Expanded(
                              flex: 7,
                              child: _buildProductDetails(context),
                            ),
                          ],
                        ),
                        const SizedBox(height: TSizes.spaceBtwItems),
                        _buildDescriptionAndReviews(context, product),
                        const SizedBox(height: TSizes.spaceBtwItems),
                        _buildRelatedProducts(context),
                      ],
                    );
                  } else {
                    return Column(
                      children: [
                        TProductImageSlider(product: product),
                        const SizedBox(height: TSizes.spaceBtwItems),
                        _buildProductDetails(context),
                        const SizedBox(height: TSizes.spaceBtwItems),
                        _buildDescriptionAndReviews(context, product),
                        const SizedBox(height: TSizes.spaceBtwItems),
                        _buildRelatedProducts(context),
                      ],
                    );
                  }
                },
              ),
            ),
          ),
        ),
      ),
      bottomNavigationBar: LayoutBuilder(
        builder: (context, constraints) {
          final isDesktop = constraints.maxWidth > 768;
          return isDesktop
              ? const SizedBox.shrink()
              : TBottomAddToCart(product: product);
        },
      ),
      floatingActionButton: Obx(
        () => cartController.cartItems.isNotEmpty
            ? FloatingActionButton.extended(
                backgroundColor: TColors.primary,
                elevation: 3,
                onPressed: () {
                  final authController = AuthenticationRepository.instance;
                  if (authController.isUserLoggedIn()) {
                    context.go('/checkout');
                  } else {
                    context.go('/login?from=/checkout');
                  }
                },
                label: Row(
                  children: [
                    Text(
                      'Checkout',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                    ),
                    const SizedBox(width: TSizes.spaceBtwItems / 2),
                    const Icon(Icons.arrow_forward,
                        color: Colors.white, size: 16)
                  ],
                ),
              )
            : const SizedBox(),
      ),
    );
  }

  Widget _buildProductDetails(BuildContext context) {
    final isDesktop = MediaQuery.of(context).size.width > 768;

    return Container(
      padding: const EdgeInsets.all(TSizes.defaultSpace),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: BorderRadius.circular(TSizes.cardRadiusLg),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 0),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TProductMetaData(product: product),
          const SizedBox(height: TSizes.md),
          Obx(() {
            final actualRating =
                reviewController.getAverageRatingForProduct(product.id);
            final actualReviewCount =
                reviewController.getReviewCountForProduct(product.id);
            return TRatingAndShare(
              rating: actualRating > 0 ? actualRating : product.averageRating,
              ratingCount: actualReviewCount > 0
                  ? actualReviewCount
                  : product.ratingCount,
              product: product,
            );
          }),
          const SizedBox(height: TSizes.defaultSpace),
          if (product.productVariations?.isNotEmpty ?? false) ...[
            ProductAttributes(product: product),
            const SizedBox(height: TSizes.defaultSpace),
          ],
          if (isDesktop) ...[
            Container(
              padding: const EdgeInsets.all(TSizes.md),
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.05),
                borderRadius: BorderRadius.circular(TSizes.cardRadiusMd),
              ),
              child: TBottomAddToCart(product: product),
            ),
            const SizedBox(height: TSizes.defaultSpace),
            _buildShopInformation(context),
          ],
          if (!isDesktop) ...[
            const SizedBox(height: TSizes.defaultSpace),
            _buildShopInformation(context),
          ],
        ],
      ),
    );
  }

  Widget _buildDescriptionAndReviews(
      BuildContext context, ProductModel product) {
    return _DynamicTabSection(product: product);
  }

  Widget _buildRelatedProducts(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 768;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: TSizes.defaultSpace),
        const Text(
          'You might like',
          style: TextStyle(fontWeight: FontWeight.w600, fontSize: 18),
        ),
        const SizedBox(height: TSizes.md),
        FutureBuilder<List<ProductModel>>(
          future: ProductController.instance.fetchRelatedProducts(product.id),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            }

            if (snapshot.hasError) {
              return Center(child: Text('Error: ${snapshot.error}'));
            }

            List<ProductModel> relatedProducts = snapshot.data ?? [];

            // If no related products, fallback to all products minus current
            if (relatedProducts.isEmpty) {
              return FutureBuilder<List<ProductModel>>(
                future:
                    Future.value(ProductController.instance.allProducts.value),
                builder: (context, allSnapshot) {
                  if (allSnapshot.connectionState == ConnectionState.waiting) {
                    return const Center(child: CircularProgressIndicator());
                  }
                  if (allSnapshot.hasError) {
                    return Center(child: Text('Error: ${allSnapshot.error}'));
                  }

                  final allProducts = allSnapshot.data ?? [];
                  final fallbackProducts = allProducts
                      .where((p) => p.id != product.id)
                      .toList()
                    ..shuffle();

                  final fallbackLimited = fallbackProducts.take(6).toList();

                  if (fallbackLimited.isEmpty) {
                    return const Center(
                        child: Text('No products to recommend.'));
                  }

                  if (isMobile) {
                    return SizedBox(
                      height: 280,
                      child: ListView.separated(
                        scrollDirection: Axis.horizontal,
                        itemCount: fallbackLimited.length,
                        separatorBuilder: (_, __) => const SizedBox(width: 12),
                        itemBuilder: (context, index) {
                          return SizedBox(
                            width: 180,
                            child: TProductCardVertical(
                              product: fallbackLimited[index],
                            ),
                          );
                        },
                      ),
                    );
                  } else {
                    return TGridLayout(
                      itemCount: fallbackLimited.length,
                      itemBuilder: (_, index) => TProductCardVertical(
                        product: fallbackLimited[index],
                      ),
                    );
                  }
                },
              );
            }

            // Otherwise, show related products normally
            if (isMobile) {
              return SizedBox(
                height: 280,
                child: ListView.separated(
                  scrollDirection: Axis.horizontal,
                  itemCount: relatedProducts.length,
                  separatorBuilder: (_, __) => const SizedBox(width: 12),
                  itemBuilder: (context, index) {
                    return SizedBox(
                      width: 180,
                      child: TProductCardVertical(
                        product: relatedProducts[index],
                      ),
                    );
                  },
                ),
              );
            } else {
              return TGridLayout(
                itemCount: relatedProducts.length.clamp(0, 6),
                itemBuilder: (_, index) => TProductCardVertical(
                  product: relatedProducts[index],
                ),
              );
            }
          },
        ),
        const SizedBox(height: TSizes.md),
        if (!isMobile)
          Align(
            alignment: Alignment.centerRight,
            child: TextButton(
              onPressed: () {
                // Implement navigation to "More Products"
              },
              child: const Text(''),
            ),
          ),
      ],
    );
  }

  Widget _buildShopInformation(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        FutureBuilder<ShopModel?>(
          future: ShopRepository.instance.getShopById(product.shopId ?? ''),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const SizedBox(
                  height: 60,
                  child: Center(child: CircularProgressIndicator()));
            }
            if (!snapshot.hasData || snapshot.data == null) {
              return const Text('Shop information not available');
            }
            final shop = snapshot.data!;
            return GestureDetector(
              onTap: () => _navigateToShop(context, shop),
              child: Container(
                margin:
                    const EdgeInsets.symmetric(vertical: TSizes.spaceBtwItems),
                padding: const EdgeInsets.all(TSizes.md),
                decoration: BoxDecoration(
                  color: Colors.grey.withOpacity(0.05),
                  borderRadius: BorderRadius.circular(TSizes.cardRadiusMd),
                ),
                child: Row(
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.grey.withOpacity(0.2),
                      ),
                      child: shop.logo?.isNotEmpty == true
                          ? ClipOval(
                              child:
                                  Image.network(shop.logo!, fit: BoxFit.cover),
                            )
                          : const Icon(Icons.store, color: TColors.primary),
                    ),
                    const SizedBox(width: TSizes.spaceBtwItems),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('Seller:',
                              style: Theme.of(context).textTheme.bodySmall),
                          Text(
                            shop.name,
                            style: Theme.of(context)
                                .textTheme
                                .titleMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: TColors.primary,
                                ),
                          ),
                          GestureDetector(
                            onTap: () => _navigateToShop(context, shop),
                            child: Text(
                              'Visit Shop',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall
                                  ?.copyWith(
                                    color: TColors.primary,
                                    decoration: TextDecoration.underline,
                                  ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.arrow_forward_ios, size: 16),
                      onPressed: () => _navigateToShop(context, shop),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
        // NEW BANNER BELOW SHOP INFO
        LayoutBuilder(
          builder: (context, constraints) {
            final isMobile = constraints.maxWidth < 600;
            return Container(
              margin: const EdgeInsets.only(top: TSizes.spaceBtwItems),
              padding: const EdgeInsets.all(TSizes.md),
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.05),
                borderRadius: BorderRadius.circular(TSizes.cardRadiusMd),
              ),
              child: isMobile
                  ? Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildBannerItem(
                          icon: Icons.local_shipping,
                          title: 'Free Shipping',
                          subtitle: 'On orders over \$50',
                        ),
                        const SizedBox(height: 12),
                        _buildBannerItem(
                          icon: Icons.lock,
                          title: 'Secure Payment',
                          subtitle: '100% secure processing',
                        ),
                        const SizedBox(height: 12),
                        _buildBannerItem(
                          icon: Icons.assignment_return,
                          title: 'Easy Returns',
                          subtitle: '30-day return policy',
                        ),
                      ],
                    )
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        _buildBannerItem(
                          icon: Icons.local_shipping,
                          title: 'Free Shipping',
                          subtitle: 'On orders over GHC150',
                        ),
                        _buildBannerItem(
                          icon: Icons.lock,
                          title: 'Secure Payment',
                          subtitle: '100% secure processing',
                        ),
                        _buildBannerItem(
                          icon: Icons.assignment_return,
                          title: 'Easy Returns',
                          subtitle: '30-day return policy',
                        ),
                      ],
                    ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildBannerItem({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Row(
      children: [
        Icon(icon, color: TColors.primary),
        const SizedBox(width: 8),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
            Text(
              subtitle,
              style: const TextStyle(fontSize: 12, color: Colors.black54),
            ),
          ],
        ),
      ],
    );
  }
}

// Complete Dynamic Tab Section with TabBar and Content
class _DynamicTabSection extends StatefulWidget {
  const _DynamicTabSection({required this.product});

  final ProductModel product;

  @override
  State<_DynamicTabSection> createState() => _DynamicTabSectionState();
}

class _DynamicTabSectionState extends State<_DynamicTabSection>
    with TickerProviderStateMixin {
  late TabController _tabController;
  double _currentHeight = 300.0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _tabController.addListener(_updateHeight);

    // Initialize and load reviews for this product
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final reviewController = Get.find<ProductReviewController>();
      reviewController.loadProductReviews(widget.product.id);
    });
  }

  @override
  void dispose() {
    _tabController.removeListener(_updateHeight);
    _tabController.dispose();
    super.dispose();
  }

  void _updateHeight() {
    setState(() {
      switch (_tabController.index) {
        case 0: // Description tab
          _currentHeight = _calculateDescriptionHeight();
          break;
        case 1: // Reviews tab
          _currentHeight = _calculateReviewsHeight();
          break;
        case 2: // Shipping tab
          _currentHeight = _calculateShippingHeight();
          break;
      }
    });
  }

  double _calculateDescriptionHeight() {
    final description =
        widget.product.description ?? 'No description available';

    // Base height for container padding and headers
    double baseHeight = 200.0;

    // Calculate description text height (rough estimation)
    final descriptionLines = (description.length / 80).ceil();
    final descriptionHeight = descriptionLines * 20.0;

    // Add height for product info grid
    double productInfoHeight = 0.0;
    int infoItems = 0;

    if (widget.product.sku?.isNotEmpty == true) infoItems++;
    if (widget.product.brand?.name.isNotEmpty == true) infoItems++;
    if (widget.product.categoryId?.isNotEmpty == true) infoItems++;
    if (widget.product.stock > 0) infoItems++;
    if (widget.product.averageRating > 0) infoItems++;
    if (widget.product.ratingCount > 0) infoItems++;

    productInfoHeight = infoItems * 35.0; // Approximate height per info item

    final totalHeight = baseHeight + descriptionHeight + productInfoHeight;
    return totalHeight.clamp(400.0, 800.0); // Min 400, max 800
  }

  double _calculateReviewsHeight() {
    final reviewController = Get.find<ProductReviewController>();
    final reviews = reviewController.productReviews;

    if (reviews.isEmpty) {
      return 300.0; // Height for empty state
    }

    // Base height for header and padding
    double baseHeight = 200.0;

    // Height per review card (approximate)
    double reviewsHeight = reviews.length * 120.0;

    final totalHeight = baseHeight + reviewsHeight;
    return totalHeight.clamp(400.0, 1000.0); // Min 400, max 1000
  }

  double _calculateShippingHeight() {
    // Base height for shipping content
    double baseHeight = 200.0;

    // Height for product info section
    double productInfoHeight = 150.0;

    // Height for policies section
    double policiesHeight = 200.0;

    final totalHeight = baseHeight + productInfoHeight + policiesHeight;
    return totalHeight.clamp(400.0, 600.0); // Min 400, max 600
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(vertical: TSizes.spaceBtwItems),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Clean Tab Bar Design
          Container(
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(8),
            ),
            padding: const EdgeInsets.all(4),
            child: TabBar(
              controller: _tabController,
              labelColor: TColors.primary,
              unselectedLabelColor: Colors.grey.shade600,
              indicator: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(6),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              indicatorPadding: const EdgeInsets.all(6),
              dividerColor: Colors.transparent,
              isScrollable: false,
              labelPadding:
                  const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              labelStyle: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
              unselectedLabelStyle: const TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
              tabs: [
                const Tab(text: "Description"),
                Tab(
                  child: Obx(() {
                    final reviewController =
                        Get.find<ProductReviewController>();
                    final reviewCount = reviewController
                        .getReviewCountForProduct(widget.product.id);
                    return Text("Reviews ($reviewCount)");
                  }),
                ),
                const Tab(text: "Shipping & Returns"),
              ],
            ),
          ),
          const SizedBox(height: TSizes.sm),
          // Dynamic content container that expands based on tab content
          SizedBox(
            height: _currentHeight,
            child: TabBarView(
              controller: _tabController,
              children: [
                // Description Tab
                _buildDescriptionContent(),
                // Reviews Tab
                _buildReviewsContent(),
                // Shipping Tab
                _buildShippingContent(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDescriptionContent() {
    return Padding(
      padding: const EdgeInsets.all(TSizes.md),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Product Description Section
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(TSizes.md),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(
                      Icons.description_outlined,
                      color: TColors.primary,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Product Description',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: TColors.primary,
                          ),
                    ),
                  ],
                ),
                const SizedBox(height: TSizes.sm),
                ReadMoreText(
                  widget.product.description ?? 'No description available',
                  trimLines: 6,
                  colorClickableText: TColors.primary,
                  trimMode: TrimMode.Line,
                  trimCollapsedText: ' Show more',
                  trimExpandedText: ' Show less',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        height: 1.6,
                        color: Colors.grey.shade700,
                      ),
                ),
              ],
            ),
          ),

          const SizedBox(height: TSizes.sm),

          // Product Details Section
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(TSizes.md),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(
                      Icons.info_outline,
                      color: TColors.primary,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Product Details',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: TColors.primary,
                          ),
                    ),
                  ],
                ),
                const SizedBox(height: TSizes.sm),

                // Product Information Grid
                _buildProductInfoGrid(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductInfoGrid() {
    final productInfo = <String, String>{};

    // Add available product information
    if (widget.product.sku?.isNotEmpty == true) {
      productInfo['SKU'] = widget.product.sku!;
    }
    if (widget.product.brand?.name.isNotEmpty == true) {
      productInfo['Brand'] = widget.product.brand!.name;
    }
    if (widget.product.categoryId?.isNotEmpty == true) {
      productInfo['Category'] = widget.product.categoryId!;
    }
    if (widget.product.stock > 0) {
      productInfo['Stock'] = '${widget.product.stock} units';
    }
    if (widget.product.averageRating > 0) {
      productInfo['Rating'] =
          '${widget.product.averageRating.toStringAsFixed(1)} ⭐';
    }
    if (widget.product.ratingCount > 0) {
      productInfo['Reviews'] = '${widget.product.ratingCount} reviews';
    }

    if (productInfo.isEmpty) {
      return Text(
        'No additional product details available',
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey.shade600,
              fontStyle: FontStyle.italic,
            ),
      );
    }

    return Column(
      children: productInfo.entries
          .map((entry) => Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      width: 80,
                      child: Text(
                        '${entry.key}:',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: Colors.grey.shade700,
                            ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        entry.value,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey.shade800,
                            ),
                      ),
                    ),
                  ],
                ),
              ))
          .toList(),
    );
  }

  Widget _buildReviewsContent() {
    return Padding(
      padding: const EdgeInsets.all(TSizes.md),
      child: Obx(() {
        final reviewController = Get.find<ProductReviewController>();
        final reviews = reviewController.productReviews;

        if (reviewController.isLoading.value) {
          return const Center(
            child: Padding(
              padding: EdgeInsets.all(TSizes.xl),
              child: CircularProgressIndicator(),
            ),
          );
        }

        if (reviews.isEmpty) {
          return Container(
            width: double.infinity,
            padding: const EdgeInsets.all(TSizes.xl),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.rate_review_outlined,
                  size: 64,
                  color: Colors.grey.shade400,
                ),
                const SizedBox(height: TSizes.spaceBtwItems),
                Text(
                  'No reviews yet',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Colors.grey.shade600,
                        fontWeight: FontWeight.w600,
                      ),
                ),
                const SizedBox(height: TSizes.sm),
                Text(
                  'Be the first to review this product',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey.shade500,
                      ),
                ),
              ],
            ),
          );
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Reviews Header with Statistics
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(TSizes.md),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Row(
                        children: [
                          Icon(Icons.star_outline,
                              color: TColors.primary, size: 20),
                          SizedBox(width: 8),
                          Text(
                            'Customer Reviews', /* ... */
                          ),
                        ],
                      ),
                      Text(
                          '${reviews.length} review${reviews.length != 1 ? 's' : ''}'),
                    ],
                  ),
                  // Dynamic rating badge
                ],
              ),
            ),
            const SizedBox(height: TSizes.spaceBtwItems),

            // Reviews List
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: reviews.length,
              separatorBuilder: (_, __) =>
                  const SizedBox(height: TSizes.spaceBtwItems),
              itemBuilder: (_, index) => _buildReviewCard(reviews[index]),
            ),
          ],
        );
      }),
    );
  }

  Widget _buildReviewCard(dynamic review) {
    return Container(
      padding: const EdgeInsets.all(TSizes.md),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 20,
                backgroundColor: TColors.primary.withOpacity(0.1),
                child: Text(
                  (review.userName ?? 'A').substring(0, 1).toUpperCase(),
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: TColors.primary,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      review.userName ?? 'Anonymous',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                    ),
                    Row(
                      children: [
                        ...List.generate(
                            5,
                            (index) => Icon(
                                  Icons.star,
                                  size: 14,
                                  color: index < review.rating
                                      ? Colors.amber
                                      : Colors.grey.shade300,
                                )),
                        const SizedBox(width: 8),
                        Text(
                          _formatDate(review.timestamp),
                          style:
                              Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: Colors.grey.shade600,
                                  ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (review.comment?.isNotEmpty == true) ...[
            const SizedBox(height: 12),
            Text(
              review.comment,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    height: 1.5,
                  ),
            ),
          ],
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 30) {
      return '${date.day}/${date.month}/${date.year}';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} days ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hours ago';
    } else {
      return 'Just now';
    }
  }

  Widget _buildShippingContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(TSizes.defaultSpace),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Shipping & Returns Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(TSizes.md),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.local_shipping_outlined,
                  color: TColors.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Shipping & Returns',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: TColors.primary,
                      ),
                ),
              ],
            ),
          ),
          const SizedBox(height: TSizes.spaceBtwItems),

          // Product-specific shipping information
          _buildProductShippingInfo(),

          const SizedBox(height: TSizes.spaceBtwItems),

          // General shipping policies
          _buildGeneralShippingPolicies(),
        ],
      ),
    );
  }

  Widget _buildProductShippingInfo() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(TSizes.md),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Product Information',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade800,
                ),
          ),
          const SizedBox(height: TSizes.sm),

          // Stock status
          _buildInfoRow(
            icon: widget.product.stock > 0
                ? Icons.check_circle_outline
                : Icons.error_outline,
            label: 'Availability',
            value: widget.product.stock > 0
                ? 'In Stock (${widget.product.stock} units)'
                : 'Out of Stock',
            valueColor: widget.product.stock > 0 ? Colors.green : Colors.red,
          ),

          const SizedBox(height: 8),

          // Estimated delivery based on stock
          _buildInfoRow(
            icon: Icons.schedule_outlined,
            label: 'Estimated Delivery',
            value: widget.product.stock > 0
                ? '2-4 business days'
                : 'Currently unavailable',
            valueColor:
                widget.product.stock > 0 ? Colors.grey.shade700 : Colors.red,
          ),
        ],
      ),
    );
  }

  Widget _buildGeneralShippingPolicies() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(TSizes.md),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Shipping & Return Policies',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade800,
                ),
          ),
          const SizedBox(height: TSizes.sm),
          _buildPolicyItem(
            icon: Icons.local_shipping,
            title: 'Standard Shipping',
            description: 'Free shipping on orders over \$50',
          ),
          _buildPolicyItem(
            icon: Icons.refresh,
            title: 'Returns',
            description: '30-day return policy for unused items',
          ),
          _buildPolicyItem(
            icon: Icons.security,
            title: 'Secure Delivery',
            description: 'All packages are tracked and insured',
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow({
    required IconData icon,
    required String label,
    required String value,
    Color? valueColor,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: Colors.grey.shade600,
        ),
        const SizedBox(width: 8),
        Text(
          '$label:',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            value,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: valueColor ?? Colors.grey.shade800,
                  fontWeight: FontWeight.w600,
                ),
          ),
        ),
      ],
    );
  }

  Widget _buildPolicyItem({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            size: 18,
            color: TColors.primary,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: Colors.grey.shade800,
                      ),
                ),
                const SizedBox(height: 2),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey.shade600,
                        height: 1.4,
                      ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
