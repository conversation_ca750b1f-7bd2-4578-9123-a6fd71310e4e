
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:readmore/readmore.dart';

import 'package:demarth/data/repositories/authentication/authentication_repository.dart';
import 'package:demarth/data/repositories/shop/shop_repository.dart';
import 'package:demarth/features/shop/controllers/product/cart_controller.dart';
import 'package:demarth/features/shop/controllers/product/product_controller.dart';
import 'package:demarth/features/shop/controllers/product_review_controller.dart';
import 'package:demarth/features/shop/models/product_model.dart';
import 'package:demarth/features/shop/models/shop_model.dart';
import 'package:demarth/features/shop/screens/product_reviews/product_reviews.dart';
import 'package:demarth/features/shop/screens/product_detail/widgets/rating_share_widget.dart';
import 'package:demarth/features/shop/screens/product_detail/widgets/product_meta_data.dart';
import 'package:demarth/features/shop/screens/product_detail/widgets/product_attributes.dart';
import 'package:demarth/features/shop/screens/product_detail/widgets/product_detail_image_slider.dart';

import '../../../../common/widgets/custom_shapes/containers/web_navbar.dart';
import '../../../../common/widgets/layouts/base_layout.dart';
import '../../../../common/widgets/layouts/grid_layout.dart';
import '../../../../common/widgets/products/cart/bottom_add_to_cart_widget.dart';
import '../../../../common/widgets/products/product_cards/product_card_vertical.dart';
import '../../../../utils/constants/colors.dart';
import '../../../../utils/constants/sizes.dart';

class ProductDetailScreen extends StatelessWidget {
  const ProductDetailScreen({
    super.key,
    this.product,
    this.productId,
  });

  final ProductModel? product;
  final String? productId;

  static String generateSlug(String name) {
    return name
        .toLowerCase()
        .replaceAll(' ', '-')
        .replaceAll(RegExp(r'[^\w-]'), '');
  }

  static String? extractProductId(String slug) {
    final parts = slug.split('-');
    return parts.isNotEmpty ? parts.last : null;
  }

  @override
  Widget build(BuildContext context) {
    final cartController = CartController.instance;
    final reviewController = Get.put(ProductReviewController());

    if (!Get.isRegistered<ShopRepository>()) {
      Get.put(ShopRepository());
    }

    if (product == null && productId != null) {
      return FutureBuilder<ProductModel?>(
        future: ProductController.instance.getProductById(productId!),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError || !snapshot.hasData) {
            return Center(
              child: Text('Product not found',
                  style: Theme.of(context).textTheme.headlineSmall),
            );
          }
          return _ProductDetailContent(
            product: snapshot.data!,
            reviewController: reviewController,
            cartController: cartController,
          );
        },
      );
    }

    if (product == null) {
      return Center(
        child: Text('Product not found',
            style: Theme.of(context).textTheme.headlineSmall),
      );
    }

    reviewController.loadProductReviews(product!.id);

    return _ProductDetailContent(
      product: product!,
      reviewController: reviewController,
      cartController: cartController,
    );
  }
}

class _ProductDetailContent extends StatelessWidget {
  const _ProductDetailContent({
    required this.product,
    required this.reviewController,
    required this.cartController,
  });

  final ProductModel product;
  final ProductReviewController reviewController;
  final CartController cartController;

  void _navigateToShop(BuildContext context, ShopModel shop) {
    if (product.shopId == null || product.shopId!.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Shop information is incomplete')),
      );
      return;
    }
    context.go('/shop/${product.shopId}', extra: shop);
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 768;

    return Scaffold(
      body: TBaseLayout(
        appBar: isMobile
            ? null
            : const PreferredSize(
                preferredSize: Size.fromHeight(160),
                child: TWebNavBar(),
              ),
        child: Center(
          child: ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 1200),
            child: Padding(
              padding: const EdgeInsets.all(TSizes.defaultSpace),
              child: LayoutBuilder(
                builder: (context, constraints) {
                  final isDesktop = constraints.maxWidth > 768;

                  if (isDesktop) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              flex: 5,
                              child: Column(
                                children: [
                                  TProductImageSlider(product: product),
                                  const SizedBox(height: TSizes.spaceBtwItems),
                                ],
                              ),
                            ),
                            const SizedBox(width: TSizes.spaceBtwItems),
                            Expanded(
                              flex: 7,
                              child: _buildProductDetails(context),
                            ),
                          ],
                        ),
                        const SizedBox(height: TSizes.spaceBtwItems),
                        _buildDescriptionAndReviews(context, product),
                        const SizedBox(height: TSizes.spaceBtwItems),
                        _buildRelatedProducts(context),
                      ],
                    );
                  } else {
                    return Column(
                      children: [
                        TProductImageSlider(product: product),
                        const SizedBox(height: TSizes.spaceBtwItems),
                        _buildProductDetails(context),
                        const SizedBox(height: TSizes.spaceBtwItems),
                        _buildDescriptionAndReviews(context, product),
                        const SizedBox(height: TSizes.spaceBtwItems),
                        _buildRelatedProducts(context),
                      ],
                    );
                  }
                },
              ),
            ),
          ),
        ),
      ),
      bottomNavigationBar: LayoutBuilder(
        builder: (context, constraints) {
          final isDesktop = constraints.maxWidth > 768;
          return isDesktop
              ? const SizedBox.shrink()
              : TBottomAddToCart(product: product);
        },
      ),
      floatingActionButton: Obx(
        () => cartController.cartItems.isNotEmpty
            ? FloatingActionButton.extended(
                backgroundColor: TColors.primary,
                elevation: 3,
                onPressed: () {
                  final authController = AuthenticationRepository.instance;
                  if (authController.isUserLoggedIn()) {
                    context.go('/checkout');
                  } else {
                    context.go('/login?from=/checkout');
                  }
                },
                label: Row(
                  children: [
                    Text(
                      'Checkout',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                    ),
                    const SizedBox(width: TSizes.spaceBtwItems / 2),
                    const Icon(Icons.arrow_forward,
                        color: Colors.white, size: 16)
                  ],
                ),
              )
            : const SizedBox(),
      ),
    );
  }

  Widget _buildProductDetails(BuildContext context) {
    final isDesktop = MediaQuery.of(context).size.width > 768;

    return Container(
      padding: const EdgeInsets.all(TSizes.defaultSpace),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: BorderRadius.circular(TSizes.cardRadiusLg),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 0),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TProductMetaData(product: product),
          const SizedBox(height: TSizes.md),
          Obx(() {
            final actualRating =
                reviewController.getAverageRatingForProduct(product.id);
            final actualReviewCount =
                reviewController.getReviewCountForProduct(product.id);
            return TRatingAndShare(
              rating: actualRating > 0 ? actualRating : product.averageRating,
              ratingCount: actualReviewCount > 0
                  ? actualReviewCount
                  : product.ratingCount,
              product: product,
            );
          }),
          const SizedBox(height: TSizes.defaultSpace),
          if (product.productVariations?.isNotEmpty ?? false) ...[
            ProductAttributes(product: product),
            const SizedBox(height: TSizes.defaultSpace),
          ],
          if (isDesktop) ...[
            Container(
              padding: const EdgeInsets.all(TSizes.md),
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.05),
                borderRadius: BorderRadius.circular(TSizes.cardRadiusMd),
              ),
              child: TBottomAddToCart(product: product),
            ),
            const SizedBox(height: TSizes.defaultSpace),
            _buildShopInformation(context),
          ],
          if (!isDesktop) ...[
            const SizedBox(height: TSizes.defaultSpace),
            _buildShopInformation(context),
          ],
        ],
      ),
    );
  }

  Widget _buildDescriptionAndReviews(
      BuildContext context, ProductModel product) {
    return DefaultTabController(
      length: 3,
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: BorderRadius.circular(TSizes.cardRadiusLg),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 0),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Container(
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.05),
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(TSizes.cardRadiusLg),
                ),
              ),
              child: TabBar(
                labelColor: TColors.primary,
                unselectedLabelColor: Colors.grey[600],
                indicatorColor: TColors.primary,
                indicatorWeight: 3,
                labelStyle: const TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 15,
                ),
                unselectedLabelStyle: const TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 15,
                ),
                tabs: const [
                  Tab(
                      child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.description_outlined, size: 20),
                      SizedBox(width: 8),
                      Text("Description"),
                    ],
                  )),
                  Tab(
                      child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.star_outline, size: 20),
                      SizedBox(width: 8),
                      Text("Reviews"),
                    ],
                  )),
                  Tab(
                      child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.local_shipping_outlined, size: 20),
                      SizedBox(width: 8),
                      Text("Shipping"),
                    ],
                  )),
                ],
              ),
            ),
            // Dynamic content container that expands based on tab content
            _DynamicTabContent(product: product),
          ],
        ),
      ),
    );
  }

  Widget _buildRelatedProducts(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 768;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: TSizes.defaultSpace),
        const Text(
          'You might like',
          style: TextStyle(fontWeight: FontWeight.w600, fontSize: 18),
        ),
        const SizedBox(height: TSizes.md),
        FutureBuilder<List<ProductModel>>(
          future: ProductController.instance.fetchRelatedProducts(product.id),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            }

            if (snapshot.hasError) {
              return Center(child: Text('Error: ${snapshot.error}'));
            }

            List<ProductModel> relatedProducts = snapshot.data ?? [];

            // If no related products, fallback to all products minus current
            if (relatedProducts.isEmpty) {
              return FutureBuilder<List<ProductModel>>(
                future:
                    Future.value(ProductController.instance.allProducts.value),
                builder: (context, allSnapshot) {
                  if (allSnapshot.connectionState == ConnectionState.waiting) {
                    return const Center(child: CircularProgressIndicator());
                  }
                  if (allSnapshot.hasError) {
                    return Center(child: Text('Error: ${allSnapshot.error}'));
                  }

                  final allProducts = allSnapshot.data ?? [];
                  final fallbackProducts = allProducts
                      .where((p) => p.id != product.id)
                      .toList()
                    ..shuffle();

                  final fallbackLimited = fallbackProducts.take(6).toList();

                  if (fallbackLimited.isEmpty) {
                    return const Center(
                        child: Text('No products to recommend.'));
                  }

                  if (isMobile) {
                    return SizedBox(
                      height: 280,
                      child: ListView.separated(
                        scrollDirection: Axis.horizontal,
                        itemCount: fallbackLimited.length,
                        separatorBuilder: (_, __) => const SizedBox(width: 12),
                        itemBuilder: (context, index) {
                          return SizedBox(
                            width: 180,
                            child: TProductCardVertical(
                              product: fallbackLimited[index],
                            ),
                          );
                        },
                      ),
                    );
                  } else {
                    return TGridLayout(
                      itemCount: fallbackLimited.length,
                      itemBuilder: (_, index) => TProductCardVertical(
                        product: fallbackLimited[index],
                      ),
                    );
                  }
                },
              );
            }

            // Otherwise, show related products normally
            if (isMobile) {
              return SizedBox(
                height: 280,
                child: ListView.separated(
                  scrollDirection: Axis.horizontal,
                  itemCount: relatedProducts.length,
                  separatorBuilder: (_, __) => const SizedBox(width: 12),
                  itemBuilder: (context, index) {
                    return SizedBox(
                      width: 180,
                      child: TProductCardVertical(
                        product: relatedProducts[index],
                      ),
                    );
                  },
                ),
              );
            } else {
              return TGridLayout(
                itemCount: relatedProducts.length.clamp(0, 6),
                itemBuilder: (_, index) => TProductCardVertical(
                  product: relatedProducts[index],
                ),
              );
            }
          },
        ),
        const SizedBox(height: TSizes.md),
        if (!isMobile)
          Align(
            alignment: Alignment.centerRight,
            child: TextButton(
              onPressed: () {
                // Implement navigation to "More Products"
              },
              child: const Text(''),
            ),
          ),
      ],
    );
  }

  Widget _buildShopInformation(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        FutureBuilder<ShopModel?>(
          future: ShopRepository.instance.getShopById(product.shopId ?? ''),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const SizedBox(
                  height: 60,
                  child: Center(child: CircularProgressIndicator()));
            }
            if (!snapshot.hasData || snapshot.data == null) {
              return const Text('Shop information not available');
            }
            final shop = snapshot.data!;
            return GestureDetector(
              onTap: () => _navigateToShop(context, shop),
              child: Container(
                margin:
                    const EdgeInsets.symmetric(vertical: TSizes.spaceBtwItems),
                padding: const EdgeInsets.all(TSizes.md),
                decoration: BoxDecoration(
                  color: Colors.grey.withOpacity(0.05),
                  borderRadius: BorderRadius.circular(TSizes.cardRadiusMd),
                ),
                child: Row(
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.grey.withOpacity(0.2),
                      ),
                      child: shop.logo?.isNotEmpty == true
                          ? ClipOval(
                              child:
                                  Image.network(shop.logo!, fit: BoxFit.cover),
                            )
                          : const Icon(Icons.store, color: TColors.primary),
                    ),
                    const SizedBox(width: TSizes.spaceBtwItems),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('Seller:',
                              style: Theme.of(context).textTheme.bodySmall),
                          Text(
                            shop.name,
                            style: Theme.of(context)
                                .textTheme
                                .titleMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: TColors.primary,
                                ),
                          ),
                          GestureDetector(
                            onTap: () => _navigateToShop(context, shop),
                            child: Text(
                              'Visit Shop',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall
                                  ?.copyWith(
                                    color: TColors.primary,
                                    decoration: TextDecoration.underline,
                                  ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.arrow_forward_ios, size: 16),
                      onPressed: () => _navigateToShop(context, shop),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
        // NEW BANNER BELOW SHOP INFO
        LayoutBuilder(
          builder: (context, constraints) {
            final isMobile = constraints.maxWidth < 600;
            return Container(
              margin: const EdgeInsets.only(top: TSizes.spaceBtwItems),
              padding: const EdgeInsets.all(TSizes.md),
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.05),
                borderRadius: BorderRadius.circular(TSizes.cardRadiusMd),
              ),
              child: isMobile
                  ? Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildBannerItem(
                          icon: Icons.local_shipping,
                          title: 'Free Shipping',
                          subtitle: 'On orders over \$50',
                        ),
                        const SizedBox(height: 12),
                        _buildBannerItem(
                          icon: Icons.lock,
                          title: 'Secure Payment',
                          subtitle: '100% secure processing',
                        ),
                        const SizedBox(height: 12),
                        _buildBannerItem(
                          icon: Icons.assignment_return,
                          title: 'Easy Returns',
                          subtitle: '30-day return policy',
                        ),
                      ],
                    )
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        _buildBannerItem(
                          icon: Icons.local_shipping,
                          title: 'Free Shipping',
                          subtitle: 'On orders over GHC150',
                        ),
                        _buildBannerItem(
                          icon: Icons.lock,
                          title: 'Secure Payment',
                          subtitle: '100% secure processing',
                        ),
                        _buildBannerItem(
                          icon: Icons.assignment_return,
                          title: 'Easy Returns',
                          subtitle: '30-day return policy',
                        ),
                      ],
                    ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildBannerItem({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Row(
      children: [
        Icon(icon, color: TColors.primary),
        const SizedBox(width: 8),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
            Text(
              subtitle,
              style: const TextStyle(fontSize: 12, color: Colors.black54),
            ),
          ],
        ),
      ],
    );
  }
}

// Dynamic Tab Content Widget that expands based on content
class _DynamicTabContent extends StatelessWidget {
  const _DynamicTabContent({required this.product});

  final ProductModel product;

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: TabBarView(
        children: [
          // Description Tab
          SingleChildScrollView(
            padding: const EdgeInsets.all(TSizes.defaultSpace),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Product Description',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: TSizes.spaceBtwItems),
                ReadMoreText(
                  product.description ?? 'No description available',
                  trimLines: 4,
                  colorClickableText: TColors.primary,
                  trimMode: TrimMode.Line,
                  trimCollapsedText: ' Show more',
                  trimExpandedText: ' Show less',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ),
          ),
          // Reviews Tab
          Container(
            constraints: const BoxConstraints(minHeight: 300),
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(TSizes.defaultSpace),
              child: ProductReviewsScreen(product: product),
            ),
          ),
          // Shipping Tab
          SingleChildScrollView(
            padding: const EdgeInsets.all(TSizes.defaultSpace),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Shipping Information',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: TSizes.spaceBtwItems),
                const ListTile(
                  leading: Icon(Icons.local_shipping_outlined,
                      color: TColors.primary),
                  title: Text('Free Shipping'),
                  subtitle: Text('On all orders over \$50'),
                  contentPadding: EdgeInsets.zero,
                ),
                const ListTile(
                  leading: Icon(Icons.access_time, color: TColors.primary),
                  title: Text('Delivery Time'),
                  subtitle: Text('2-4 business days'),
                  contentPadding: EdgeInsets.zero,
                ),
                const ListTile(
                  leading: Icon(Icons.refresh, color: TColors.primary),
                  title: Text('Easy Returns'),
                  subtitle: Text('30-day return policy'),
                  contentPadding: EdgeInsets.zero,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
