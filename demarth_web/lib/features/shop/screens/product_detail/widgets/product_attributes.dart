import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../../utils/constants/colors.dart';
import '../../../../../utils/constants/sizes.dart';
import '../../../controllers/product/variation_controller.dart';
import '../../../models/product_model.dart';

class ProductAttributes extends StatefulWidget {
  const ProductAttributes({
    super.key,
    required this.product,
  });

  final ProductModel product;

  @override
  State<ProductAttributes> createState() => _ProductAttributesState();
}

class _ProductAttributesState extends State<ProductAttributes> {
  late final VariationController controller;

  @override
  void initState() {
    super.initState();
    controller = Get.put(VariationController());
    // Reset controller when the widget is first created
    controller.resetSelectedAttributes();
  }

  @override
  Widget build(BuildContext context) {
    // Early return if no attributes or variations
    if (widget.product.productAttributes == null ||
        widget.product.productVariations == null) {
      return const SizedBox();
    }

    return Container(
      padding: const EdgeInsets.all(TSizes.md),
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.03),
        borderRadius: BorderRadius.circular(TSizes.cardRadiusLg),
        border: Border.all(color: Colors.grey.withOpacity(0.1)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Row(
            children: [
              const Icon(
                Icons.tune,
                color: TColors.primary,
                size: 20,
              ),
              const SizedBox(width: TSizes.sm),
              Text(
                'Product Variations',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: TColors.primary,
                    ),
              ),
            ],
          ),
          const SizedBox(height: TSizes.md),

          // Display each product attribute (color, size, etc.)
          ...widget.product.productAttributes!.map((attribute) {
            return Container(
              margin: const EdgeInsets.only(bottom: TSizes.md),
              padding: const EdgeInsets.all(TSizes.sm),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(TSizes.cardRadiusMd),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Attribute name and selected value
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        attribute.name ?? '',
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: Colors.black87,
                            ),
                      ),
                      // Show selected attribute value
                      Obx(() {
                        final selectedValue =
                            controller.selectedAttributes[attribute.name ?? ''];
                        return selectedValue != null
                            ? Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: TSizes.sm,
                                  vertical: TSizes.xs,
                                ),
                                decoration: BoxDecoration(
                                  color: TColors.primary.withOpacity(0.1),
                                  borderRadius:
                                      BorderRadius.circular(TSizes.sm),
                                ),
                                child: Text(
                                  selectedValue,
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodySmall
                                      ?.copyWith(
                                        color: TColors.primary,
                                        fontWeight: FontWeight.w500,
                                      ),
                                ),
                              )
                            : Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: TSizes.sm,
                                  vertical: TSizes.xs,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.grey.withOpacity(0.1),
                                  borderRadius:
                                      BorderRadius.circular(TSizes.sm),
                                ),
                                child: Text(
                                  'Select ${attribute.name}',
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodySmall
                                      ?.copyWith(
                                        color: Colors.grey.shade600,
                                        fontStyle: FontStyle.italic,
                                      ),
                                ),
                              );
                      }),
                    ],
                  ),
                  const SizedBox(height: TSizes.sm),

                  // Display attribute values as choice chips
                  Obx(() {
                    // Get available variations for this attribute
                    final availableValues =
                        controller.getAttributesAvailabilityInVariation(
                      widget.product.productVariations!,
                      attribute.name ?? '',
                    );

                    return Wrap(
                      spacing: TSizes.sm,
                      runSpacing: TSizes.xs,
                      children: attribute.values?.map((String value) {
                            // Check if this attribute value is selected
                            final isSelected = controller
                                    .selectedAttributes[attribute.name ?? ''] ==
                                value;

                            // Check if this value is available in any variation
                            // If no values are available, consider all values available
                            final isAvailable = availableValues.isEmpty ||
                                availableValues.contains(value);

                            return _buildProfessionalChoiceChip(
                              text: value,
                              selected: isSelected,
                              isAvailable: isAvailable,
                              onSelected: isAvailable
                                  ? () {
                                      controller.onAttributeSelected(
                                          widget.product,
                                          attribute.name ?? '',
                                          value);
                                    }
                                  : null,
                            );
                          }).toList() ??
                          [],
                    );
                  }),
                ],
              ),
            );
          }),

          // Show selected variation details if any
          Obx(() {
            final selectedVariation = controller.selectedVariation.value;
            if (selectedVariation.id.isNotEmpty) {
              return Container(
                margin: const EdgeInsets.only(top: TSizes.sm),
                padding: const EdgeInsets.all(TSizes.md),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      TColors.primary.withOpacity(0.1),
                      TColors.primary.withOpacity(0.05),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(TSizes.cardRadiusMd),
                  border: Border.all(color: TColors.primary.withOpacity(0.2)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(
                          Icons.check_circle,
                          color: TColors.primary,
                          size: 18,
                        ),
                        const SizedBox(width: TSizes.xs),
                        Text(
                          'Selected Variation',
                          style:
                              Theme.of(context).textTheme.titleSmall?.copyWith(
                                    fontWeight: FontWeight.w600,
                                    color: TColors.primary,
                                  ),
                        ),
                      ],
                    ),
                    const SizedBox(height: TSizes.sm),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Price',
                                style: Theme.of(context)
                                    .textTheme
                                    .bodySmall
                                    ?.copyWith(
                                      color: Colors.grey.shade600,
                                    ),
                              ),
                              Text(
                                '\$${selectedVariation.salePrice > 0 ? selectedVariation.salePrice : selectedVariation.price}',
                                style: Theme.of(context)
                                    .textTheme
                                    .titleMedium
                                    ?.copyWith(
                                      fontWeight: FontWeight.bold,
                                      color: TColors.primary,
                                    ),
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Stock',
                                style: Theme.of(context)
                                    .textTheme
                                    .bodySmall
                                    ?.copyWith(
                                      color: Colors.grey.shade600,
                                    ),
                              ),
                              Text(
                                selectedVariation.stock > 0
                                    ? '${selectedVariation.stock} available'
                                    : 'Out of stock',
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyMedium
                                    ?.copyWith(
                                      fontWeight: FontWeight.w500,
                                      color: selectedVariation.stock > 0
                                          ? Colors.green.shade600
                                          : Colors.red.shade600,
                                    ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              );
            }
            return const SizedBox.shrink();
          }),
        ],
      ),
    );
  }

  Widget _buildProfessionalChoiceChip({
    required String text,
    required bool selected,
    required bool isAvailable,
    required VoidCallback? onSelected,
  }) {
    return GestureDetector(
      onTap: isAvailable ? onSelected : null,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(
          horizontal: TSizes.md,
          vertical: TSizes.sm,
        ),
        decoration: BoxDecoration(
          color: selected
              ? TColors.primary
              : isAvailable
                  ? Colors.white
                  : Colors.grey.shade100,
          borderRadius: BorderRadius.circular(TSizes.cardRadiusMd),
          border: Border.all(
            color: selected
                ? TColors.primary
                : isAvailable
                    ? TColors.primary.withOpacity(0.3)
                    : Colors.grey.shade300,
            width: selected ? 2 : 1,
          ),
          boxShadow: selected
              ? [
                  BoxShadow(
                    color: TColors.primary.withOpacity(0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ]
              : isAvailable
                  ? [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 4,
                        offset: const Offset(0, 1),
                      ),
                    ]
                  : [],
        ),
        child: Text(
          text,
          style: TextStyle(
            color: selected
                ? Colors.white
                : isAvailable
                    ? Colors.black87
                    : Colors.grey.shade500,
            fontWeight: selected ? FontWeight.w600 : FontWeight.w500,
            fontSize: 14,
          ),
        ),
      ),
    );
  }
}
