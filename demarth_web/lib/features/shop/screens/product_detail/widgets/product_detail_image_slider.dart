import 'package:cached_network_image/cached_network_image.dart';
import 'package:demarth/features/shop/controllers/product/images_controller.dart';
import 'package:demarth/features/shop/screens/product_detail/widgets/share_options.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';
import 'package:go_router/go_router.dart';
import '../../../../../utils/constants/colors.dart';
import '../../../controllers/product/cart_controller.dart';
import '../../../../../common/widgets/appbar/appbar.dart';
import '../../../../../common/widgets/custom_shapes/curved_edges/curved_edges_widget.dart';
import '../../../../../common/widgets/images/t_rounded_image.dart';
import '../../../../../common/widgets/products/favourite_icon/favourite_icon.dart';
import '../../../../../utils/constants/sizes.dart';
import '../../../../../utils/helpers/helper_functions.dart';
import '../../../models/product_model.dart';

class TProductImageSlider extends StatelessWidget {
  const TProductImageSlider({
    super.key,
    required this.product,
    this.isNetworkImage = true,
  });

  final ProductModel product;
  final bool isNetworkImage;

  @override
  Widget build(BuildContext context) {
    final controller = ImagesController.instance;
    final isDark = THelperFunctions.isDarkMode(context);
    final images = controller.getAllProductImages(product);

    return TCurvedEdgesWidget(
      child: Container(
        color: isDark ? TColors.darkerGrey : TColors.light,
        child: Stack(
          children: [
            /// Main Large Image
            SizedBox(
              height: 400, // ✅ Reduced from 600px to 400px (33% reduction)
              child: Padding(
                padding: const EdgeInsets.only(
                  left: TSizes.defaultSpace * 2,
                  right: TSizes.defaultSpace * 2,
                  top: TSizes.defaultSpace * 2,
                  bottom: TSizes.defaultSpace * 2, // ✅ Reduced from * 5 to * 2
                ),
                child: Center(
                  child: Obx(
                    () {
                      final image =
                          controller.selectedProductImage.value.isEmpty
                              ? product.thumbnail
                              : controller.selectedProductImage.value;
                      return GestureDetector(
                        onTap: () {
                          showDialog(
                            context: context,
                            builder: (BuildContext context) {
                              return Dialog(
                                backgroundColor: Colors.transparent,
                                insetPadding: const EdgeInsets.all(10),
                                child: Stack(
                                  alignment: Alignment.center,
                                  children: [
                                    InteractiveViewer(
                                      panEnabled: true,
                                      minScale: 0.5,
                                      maxScale: 4,
                                      child: isNetworkImage
                                          ? CachedNetworkImage(
                                              imageUrl: image,
                                              fit: BoxFit.contain,
                                              progressIndicatorBuilder:
                                                  (_, __, downloadProgress) =>
                                                      CircularProgressIndicator(
                                                value:
                                                    downloadProgress.progress,
                                                color: TColors.primary,
                                              ),
                                              errorWidget: (_, __, ___) =>
                                                  const Icon(Icons.error),
                                            )
                                          : Image(image: AssetImage(image)),
                                    ),
                                    Positioned(
                                      top: 10,
                                      right: 10,
                                      child: MouseRegion(
                                        cursor: SystemMouseCursors.click,
                                        child: GestureDetector(
                                          onTap: () => Navigator.pop(context),
                                          child: Container(
                                            decoration: BoxDecoration(
                                              color:
                                                  Colors.black.withOpacity(0.5),
                                              shape: BoxShape.circle,
                                            ),
                                            padding: const EdgeInsets.all(8),
                                            child: const Icon(
                                              Icons.close,
                                              color: Colors.white,
                                              size: 24,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          );
                        },
                        child: MouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: isNetworkImage
                              ? CachedNetworkImage(
                                  imageUrl: image,
                                  progressIndicatorBuilder:
                                      (_, __, downloadProgress) =>
                                          CircularProgressIndicator(
                                    value: downloadProgress.progress,
                                    color: TColors.primary,
                                  ),
                                  errorWidget: (_, __, ___) =>
                                      const Icon(Icons.error),
                                )
                              : Image(image: AssetImage(image)),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),

            /// Image Slider
            Positioned(
              bottom: 20, // ✅ Reduced from 60px to 20px for better fit
              left: TSizes.defaultSpace,
              child: Container(
                height: 80,
                width: MediaQuery.of(context).size.width -
                    (TSizes.defaultSpace * 2),
                decoration: BoxDecoration(
                  color: isDark
                      ? TColors.darkerGrey.withOpacity(0.7)
                      : TColors.light.withOpacity(0.7),
                  borderRadius: BorderRadius.circular(TSizes.md),
                ),
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: images.length,
                  physics: const BouncingScrollPhysics(),
                  padding: const EdgeInsets.symmetric(
                    horizontal: TSizes.sm,
                    vertical: TSizes.xs,
                  ),
                  itemBuilder: (_, index) {
                    return Obx(() {
                      final imageSelected =
                          controller.selectedProductImage.value ==
                              images[index];
                      return Padding(
                        padding:
                            const EdgeInsets.only(right: TSizes.spaceBtwItems),
                        child: TRoundedImage(
                          isNetworkImage: isNetworkImage,
                          width: 80,
                          height: 80,
                          fit: BoxFit.contain,
                          imageUrl: images[index],
                          padding: const EdgeInsets.all(TSizes.sm),
                          backgroundColor:
                              isDark ? TColors.dark : TColors.white,
                          onPressed: () {
                            controller.selectedProductImage.value =
                                images[index];
                          },
                          border: Border.all(
                            color: imageSelected
                                ? TColors.primary
                                : Colors.transparent,
                            width: imageSelected ? 2 : 1,
                          ),
                        ),
                      );
                    });
                  },
                ),
              ),
            ),

            /// Appbar Icons
            TAppBar(
              showBackArrow: MediaQuery.of(context).size.width < 768,
              actions: [
                /// Favourite Icon
                TFavouriteIcon(
                  product: product,
                  size: 18,
                ),

                /// Share Icon
                IconButton(
                  tooltip: 'Share Product',
                  icon: const Icon(
                    Icons.share,
                    size: 16,
                  ),
                  onPressed: () {
                    ShareSheet.showCustomShareSheet(
                      context,
                      title: product.title,
                      productId: product.id.toString(),
                    );
                  },
                ),

                /// Cart Icon with Quantity (only on mobile)
                Builder(
                  builder: (context) {
                    final screenWidth = MediaQuery.of(context).size.width;
                    if (screenWidth < 600) {
                      return Obx(
                        () {
                          final cartController = CartController.instance;
                          return IconButton(
                            tooltip: 'Cart',
                            icon: Badge(
                              label:
                                  Text(cartController.noOfCartItems.toString()),
                              isLabelVisible: cartController.noOfCartItems > 0,
                              backgroundColor: TColors.primary,
                              child:
                                  const Icon(Iconsax.shopping_cart, size: 16),
                            ),
                            onPressed: () => context.go('/cart'),
                          );
                        },
                      );
                    } else {
                      return const SizedBox.shrink(); // Hide on larger screens
                    }
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
