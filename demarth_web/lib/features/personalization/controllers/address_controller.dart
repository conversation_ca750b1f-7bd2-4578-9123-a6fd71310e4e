import 'package:demarth/data/repositories/authentication/authentication_repository.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import '../models/address_model.dart';
import '../../../utils/popups/loaders.dart';
import '../../../common/widgets/texts/section_heading.dart';
import '../../../data/repositories/address/address_repository.dart';
import '../../../utils/constants/image_strings.dart';
import '../../../utils/constants/sizes.dart';
import '../../../utils/helpers/cloud_helper_functions.dart';
import '../../../utils/helpers/network_manager.dart';
import '../../../utils/popups/full_screen_loader.dart';
import '../screens/address/widgets/single_address_widget.dart';

class AddressController extends GetxController {
  static AddressController get instance => Get.find();

  // Variables
  final addressRepository = Get.put(AddressRepository());

  // Form controllers
  final name = TextEditingController();
  final phoneNumber = TextEditingController();
  final street = TextEditingController();
  final postalCode = TextEditingController();
  final city = TextEditingController();
  final state = TextEditingController();
  final country = TextEditingController();
  GlobalKey<FormState> addressFormKey = GlobalKey<FormState>();

  // Observable variables
  final refreshData = false.obs;
  final Rx<bool> billingSameAsShipping = true.obs;
  final Rx<AddressModel> selectedAddress = AddressModel.empty().obs;
  final Rx<AddressModel> selectedBillingAddress = AddressModel.empty().obs;
  RxList<AddressModel> allAddresses = <AddressModel>[].obs;

  @override
  void onInit() {
    super.onInit();
    loadUserAddresses();
  }

  /// Load all addresses for the current user
  Future<void> loadUserAddresses() async {
    try {
      final addresses = await addressRepository.fetchUserAddresses();
      allAddresses.assignAll(addresses);
    } catch (e) {
      TLoaders.errorSnackBar(title: 'Error', message: e.toString());
    }
  }

  /// Fetch all user specific addresses
  Future<List<AddressModel>> allUserAddresses() async {
    try {
      final addresses = await addressRepository.fetchUserAddresses();
      selectedAddress.value = addresses.firstWhere(
        (element) => element.selectedAddress,
        orElse: () => AddressModel.empty(),
      );
      return addresses;
    } catch (e) {
      TLoaders.errorSnackBar(title: 'Address not found', message: e.toString());
      return [];
    }
  }

  /// Select an address (shipping or billing)
  Future<void> selectAddress({
    required AddressModel newSelectedAddress,
    bool isBillingAddress = false,
  }) async {
    try {
      if (!isBillingAddress) {
        final addresses = await allUserAddresses();
        for (var address in addresses) {
          if (address.selectedAddress) {
            await addressRepository.updateSelectedField(
              AuthenticationRepository.instance.getUserID,
              address.id,
              false,
            );
          }
        }

        await addressRepository.updateSelectedField(
          AuthenticationRepository.instance.getUserID,
          newSelectedAddress.id,
          true,
        );

        selectedAddress.value = newSelectedAddress;
        refreshData.toggle();
      } else {
        selectedBillingAddress.value = newSelectedAddress;
      }
    } catch (e) {
      TLoaders.errorSnackBar(
        title: 'Error in Selection',
        message: e.toString(),
      );
    }
  }

  /// Show Addresses Popup - Controller method with responsive UI
  Future<dynamic> selectNewAddressPopup(
      {required BuildContext context, bool isBillingAddress = false}) {
    // Load addresses before showing the popup
    loadUserAddresses();

    // Check if we're on a mobile device based on screen width
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 600;

    if (isMobile) {
      // Use bottom sheet for mobile
      return showModalBottomSheet(
        context: context,
        builder: (_) => SingleChildScrollView(
          child: Container(
            padding: const EdgeInsets.all(TSizes.lg),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TSectionHeading(
                    title:
                        'Select ${isBillingAddress ? 'Billing' : 'Shipping'} Address',
                    showActionButton: false),
                const SizedBox(height: TSizes.spaceBtwItems),
                FutureBuilder(
                  future: allUserAddresses(),
                  builder: (_, snapshot) {
                    /// Helper Function: Handle Loader, No Record, OR ERROR Message
                    final response =
                        TCloudHelperFunctions.checkMultiRecordState(
                            snapshot: snapshot as AsyncSnapshot<List<dynamic>>);
                    if (response != null) return response;

                    return ListView.builder(
                      shrinkWrap: true,
                      itemCount: (snapshot.data as List<AddressModel>).length,
                      itemBuilder: (_, index) => TSingleAddress(
                        address: (snapshot.data as List<AddressModel>)[index],
                        isBillingAddress: isBillingAddress,
                        onTap: () async {
                          await selectAddress(
                              newSelectedAddress:
                                  (snapshot.data as List<AddressModel>)[index],
                              isBillingAddress: isBillingAddress);
                          context.pop();
                        },
                      ),
                    );
                  },
                ),
                const SizedBox(height: TSizes.defaultSpace),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                      onPressed: () {
                        context.pop();
                        context.push('/add-new-address');
                      },
                      child: const Text('Add new address')),
                ),
              ],
            ),
          ),
        ),
      );
    } else {
      // Use dialog for desktop/tablet
      return showDialog(
        context: context,
        builder: (context) => Dialog(
          shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(TSizes.cardRadiusLg)),
          child: ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 350, maxHeight: 500),
            child: Container(
              padding: const EdgeInsets.all(TSizes.defaultSpace),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          'Select ${isBillingAddress ? 'Billing' : 'Shipping'} Address',
                          style: Theme.of(context).textTheme.headlineSmall,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      IconButton(
                        onPressed: () => context.pop(),
                        icon: const Icon(Icons.close),
                      ),
                    ],
                  ),
                  const SizedBox(height: TSizes.spaceBtwSections),

                  // Address List
                  Expanded(
                    child: Obx(
                      () => allAddresses.isEmpty
                          ? Center(
                              child: Text(
                                'No addresses found',
                                style: Theme.of(context).textTheme.bodyLarge,
                              ),
                            )
                          : ListView.separated(
                              shrinkWrap: true,
                              separatorBuilder: (_, __) => const Divider(),
                              itemCount: allAddresses.length,
                              itemBuilder: (_, index) {
                                final address = allAddresses[index];
                                return ListTile(
                                  onTap: () async {
                                    await selectAddress(
                                        newSelectedAddress: address,
                                        isBillingAddress: isBillingAddress);
                                    // Force UI update after selection
                                    allAddresses.refresh();
                                    // Ensure the dialog is dismissed
                                    context.pop();
                                  },
                                  leading:
                                      const Icon(Icons.location_on_outlined),
                                  title: Text(address.name),
                                  subtitle: Text(
                                    address.toString(),
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  trailing: Obx(() => Radio<String>(
                                        value: address.id,
                                        groupValue: isBillingAddress
                                            ? selectedBillingAddress.value.id
                                            : selectedAddress.value.id,
                                        onChanged: (value) async {
                                          await selectAddress(
                                              newSelectedAddress: address,
                                              isBillingAddress:
                                                  isBillingAddress);
                                          // Force UI update after selection
                                          allAddresses.refresh();
                                          context.pop();
                                        },
                                      )),
                                );
                              },
                            ),
                    ),
                  ),
                  const SizedBox(height: TSizes.spaceBtwSections),

                  // Add New Address Button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        context.pop(); // Close the bottom sheet
                        context
                            .go('/add-new-address'); // Navigate using GoRouter
                      },
                      child: const Text('Add New Address'),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    }
  }

  /// Initialize form fields with address values for updating
  void initUpdateAddressValues(AddressModel address) {
    name.text = address.name;
    phoneNumber.text = address.phoneNumber;
    street.text = address.street;
    postalCode.text = address.postalCode;
    city.text = address.city;
    state.text = address.state;
    country.text = address.country;
  }

  /// Reset form fields
  void resetFormFields() {
    name.clear();
    phoneNumber.clear();
    street.clear();
    postalCode.clear();
    city.clear();
    state.clear();
    country.clear();
    addressFormKey.currentState?.reset();
  }

  /// Add new Address
  Future<void> addNewAddresses() async {
    try {
      // Start Loading
      TFullScreenLoader.openLoadingDialog(
          'Storing Address...', TImages.docerAnimation);

      // Check Internet Connectivity
      final isConnected = await NetworkManager().isConnected();
      if (!isConnected) {
        TFullScreenLoader.stopLoading();
        TLoaders.errorSnackBar(
          title: 'No Internet Connection',
          message: 'Please check your internet connection and try again.',
        );
        return;
      }

      // Form Validation
      if (!addressFormKey.currentState!.validate()) {
        TFullScreenLoader.stopLoading();
        TLoaders.errorSnackBar(
          title: 'Validation Error',
          message: 'Please fill in all required fields correctly.',
        );
        return;
      }

      // Check if user is authenticated
      final userId = AuthenticationRepository.instance.getUserID;
      if (userId.isEmpty) {
        TFullScreenLoader.stopLoading();
        TLoaders.errorSnackBar(
          title: 'Authentication Error',
          message: 'Please log in to add an address.',
        );
        return;
      }

      // Save Address Data
      final address = AddressModel(
        id: '',
        name: name.text.trim(),
        phoneNumber: phoneNumber.text.trim(),
        street: street.text.trim(),
        city: city.text.trim(),
        state: state.text.trim(),
        postalCode: postalCode.text.trim(),
        country: country.text.trim(),
        selectedAddress: true,
      );

      // Debug: Check if all fields have values
      if (address.name.isEmpty ||
          address.phoneNumber.isEmpty ||
          address.street.isEmpty ||
          address.city.isEmpty ||
          address.state.isEmpty ||
          address.country.isEmpty) {
        TFullScreenLoader.stopLoading();
        TLoaders.errorSnackBar(
          title: 'Missing Information',
          message: 'Please fill in all required fields.',
        );
        return;
      }

      // Save Address and get the new ID
      final newAddressId = await addressRepository.addAddress(address, userId);

      // Update Selected Address status
      address.id = newAddressId;
      await selectAddress(newSelectedAddress: address);

      // Add new address to the allAddresses list
      allAddresses.add(address);

      // Remove Loader
      TFullScreenLoader.stopLoading();

      // Show Success Message with more details
      TLoaders.successSnackBar(
          title: 'Address Added Successfully',
          message:
              'Your new address has been saved and set as the default address.');

      // Refresh Addresses Data
      refreshData.toggle();

      // Reset fields
      resetFormFields();

      // Navigate back after successful addition and UI updates
      Get.back(); // Navigate back from the add address screen
    } catch (e) {
      // Remove Loader
      TFullScreenLoader.stopLoading();
      TLoaders.errorSnackBar(
          title: 'Failed to Add Address', message: 'Error: ${e.toString()}');
    }
  }

  /// Update Address
  Future<void> updateAddress(AddressModel address) async {
    try {
      TFullScreenLoader.openLoadingDialog(
          'Updating Address...', TImages.docerAnimation);

      final isConnected = await NetworkManager().isConnected();
      if (!isConnected) {
        TFullScreenLoader.stopLoading();
        return TLoaders.errorSnackBar(
          title: 'No Internet Connection',
          message: 'Please check your internet connection and try again.',
        );
      }

      // Add the user ID when calling updateAddress
      await addressRepository.updateAddress(
        address,
        AuthenticationRepository.instance.getUserID,
      );

      TFullScreenLoader.stopLoading();

      TLoaders.successSnackBar(
        title: 'Address Updated',
        message: 'Your address has been updated successfully.',
      );

      refreshData.toggle();
    } catch (e) {
      TFullScreenLoader.stopLoading();
      debugPrint('Error updating address: $e'); // Add this line
      TLoaders.errorSnackBar(title: 'Error', message: e.toString());
    }
  }

  /// Delete Address Confirmation
  void deleteAddressConfirmation(String addressId) {
    debugPrint('deleteAddressConfirmation called for addressId: $addressId');
    Get.defaultDialog(
      title: 'Delete Address',
      middleText: 'Are you sure you want to delete this address?',
      onConfirm: () async {
        await deleteAddress(addressId);
        Get.back();
      },
      onCancel: () => Get.back(),
    );
  }

  /// Delete Address
  Future<void> deleteAddress(String addressId) async {
    // Renamed this method
    try {
      // Start Loading
      TFullScreenLoader.openLoadingDialog(
          'Deleting Address...', TImages.docerAnimation);

      // Check Internet Connectivity
      final isConnected = await NetworkManager().isConnected();
      if (!isConnected) {
        TFullScreenLoader.stopLoading();
        return;
      }

      final userId = AuthenticationRepository.instance.authUser?.uid;
      if (userId == null || userId.isEmpty) {
        TFullScreenLoader.stopLoading();
        TLoaders.errorSnackBar(
            title: 'User ID Missing',
            message: 'Unable to delete address. User ID is not available.');
        return;
      }

      if (addressId.isEmpty) {
        TFullScreenLoader.stopLoading();
        TLoaders.errorSnackBar(
            title: 'Address ID Missing',
            message: 'Unable to delete address. Address ID is not available.');
        return;
      }

      // Debug print for userId and addressId
      print(
          'DEBUG: Deleting address - User ID: $userId, Address ID: $addressId');

      // Delete Address from Firestore
      await addressRepository.deleteUserAddress(addressId);

      // Remove address from the local list
      allAddresses.removeWhere((address) =>
          address.id == addressId); // Changed userAddresses to allAddresses

      TFullScreenLoader.stopLoading();
      TLoaders.successSnackBar(
          title: 'Address Deleted',
          message: 'Your address has been deleted successfully.');
    } catch (e) {
      TFullScreenLoader.stopLoading();
      TLoaders.errorSnackBar(title: 'Oh Snap!', message: e.toString());
    }
  }
}
