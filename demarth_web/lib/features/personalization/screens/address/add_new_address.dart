import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:iconsax/iconsax.dart';
import 'package:go_router/go_router.dart';
import '../../../../../utils/constants/sizes.dart';
import '../../../../utils/validators/validation.dart';
import '../../controllers/address_controller.dart';
import '../../../../common/widgets/layouts/profile_layout.dart';
import '../../../../utils/constants/colors.dart';

class AddNewAddressScreen extends StatelessWidget {
  const AddNewAddressScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(AddressController());
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 768;

    // Create the content widget
    final content = SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.all(isMobile ? 16.0 : 24.0),
        child: Container(
          constraints: const BoxConstraints(maxWidth: 600),
          child: Card(
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
              side: BorderSide(
                color: Colors.grey.shade200,
                width: 1,
              ),
            ),
            child: Padding(
              padding: EdgeInsets.all(isMobile ? 20.0 : 32.0),
              child: Form(
                key: controller.addressFormKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Modern Header
                    if (!isMobile) ...[
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: TColors.primary.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: const Icon(
                              Iconsax.location,
                              color: TColors.primary,
                              size: 24,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Add New Address',
                                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                  fontWeight: FontWeight.w700,
                                  color: const Color(0xFF1A1D29),
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'Add a new delivery address to your account',
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  color: Colors.grey.shade600,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      const SizedBox(height: 32),
                      Divider(color: Colors.grey.shade200),
                      const SizedBox(height: 24),
                    ],

                    // Personal Information Section
                    _buildSectionHeader(context, 'Personal Information', Iconsax.user),
                    const SizedBox(height: 16),
                    
                    _buildModernTextField(
                      controller: controller.name,
                      label: 'Full Name',
                      hint: 'Enter your full name',
                      icon: Iconsax.user,
                      validator: (value) => TValidator.validateEmptyText('Name', value),
                    ),
                    const SizedBox(height: 16),
                    
                    _buildModernTextField(
                      controller: controller.phoneNumber,
                      label: 'Phone Number',
                      hint: 'Enter your phone number',
                      icon: Iconsax.call,
                      validator: TValidator.validatePhoneNumber,
                    ),
                    
                    const SizedBox(height: 32),

                    // Address Information Section
                    _buildSectionHeader(context, 'Address Information', Iconsax.location),
                    const SizedBox(height: 16),
                    
                    _buildModernTextField(
                      controller: controller.street,
                      label: 'Street Address',
                      hint: 'Enter street address',
                      icon: Iconsax.home,
                      validator: (value) => TValidator.validateEmptyText('Street', value),
                    ),
                    const SizedBox(height: 16),

                    // City and Postal Code Row
                    Row(
                      children: [
                        Expanded(
                          child: _buildModernTextField(
                            controller: controller.city,
                            label: 'City',
                            hint: 'Enter city',
                            icon: Iconsax.building,
                            validator: (value) => TValidator.validateEmptyText('City', value),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _buildModernTextField(
                            controller: controller.postalCode,
                            label: 'Postal Code',
                            hint: 'Enter postal code',
                            icon: Iconsax.code,
                            validator: (value) => TValidator.validateEmptyText('Postal Code', value),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // State and Country Row
                    Row(
                      children: [
                        Expanded(
                          child: _buildModernTextField(
                            controller: controller.state,
                            label: 'State/Province',
                            hint: 'Enter state or province',
                            icon: Iconsax.activity,
                            validator: (value) => TValidator.validateEmptyText('State', value),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _buildModernTextField(
                            controller: controller.country,
                            label: 'Country',
                            hint: 'Enter country',
                            icon: Iconsax.global,
                            validator: (value) => TValidator.validateEmptyText('Country', value),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 40),

                    // Save Button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () async {
                          await controller.addNewAddresses();
                          // Navigate back to address list
                          if (context.mounted) {
                            context.go('/address');
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: TColors.primary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 0,
                        ),
                        child: Text(
                          'Save Address',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );

    // Return different layouts based on screen size
    if (isMobile) {
      return Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: TColors.primary),
            onPressed: () => context.go('/address'),
          ),
          title: Text(
            'Add New Address',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        body: content,
        bottomNavigationBar: null,
      );
    } else {
      return ProfileLayout(
        currentRoute: '/add-new-address',
        child: Padding(
          padding: const EdgeInsets.only(left: 10),
          child: Align(
            alignment: Alignment.centerLeft,
            child: content,
          ),
        ),
      );
    }
  }

  Widget _buildSectionHeader(BuildContext context, String title, IconData icon) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: TColors.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: TColors.primary,
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: const Color(0xFF1A1D29),
          ),
        ),
      ],
    );
  }

  Widget _buildModernTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    required String? Function(String?) validator,
  }) {
    return TextFormField(
      controller: controller,
      validator: validator,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon, color: TColors.primary.withValues(alpha: 0.7)),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: TColors.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.red, width: 1),
        ),
        filled: true,
        fillColor: Colors.grey.shade50,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      ),
    );
  }
}
