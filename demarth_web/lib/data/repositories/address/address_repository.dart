import 'dart:html' as html;
import 'package:demarth/features/authentication/controllers/login_in_controller.dart';
import 'package:demarth/features/personalization/controllers/user_controller.dart';
import 'package:demarth/firebase_options.dart';
import 'package:demarth/utils/exceptions/exceptions.dart';
import 'package:demarth/utils/popups/full_screen_loader.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:google_sign_in/google_sign_in.dart';
import '../../../utils/exceptions/firebase_auth_exceptions.dart';
import '../../../utils/exceptions/firebase_exceptions.dart';
import '../../../utils/exceptions/format_exceptions.dart';
import '../../../utils/exceptions/platform_exceptions.dart';
import '../../../utils/local_storage/storage_utility.dart';
import '../../../features/shop/controllers/product/cart_controller.dart';
import '../../../features/shop/controllers/product/favourites_controller.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:demarth/features/personalization/models/address_model.dart'; // Import AddressModel

class AddressRepository extends GetxController {
  static AddressRepository get instance => Get.find();

  final _db = FirebaseFirestore.instance; // Add Firestore instance

  // Add this property for phone authentication
  String verificationId = '';

  var deviceStorage = TLocalStorage();
  final _auth = FirebaseAuth.instance;
  late final Rx<User?> _firebaseUser;

  @override
  void onInit() {
    super.onInit();
    _firebaseUser = Rx<User?>(_auth.currentUser);
    _firebaseUser.bindStream(_auth.userChanges());
  }

  /// Initialize Authentication
  Future<void> initializeAuth() async {
    await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform);
    deviceStorage = TLocalStorage();

    FirebaseAuth.instance.authStateChanges().listen((User? user) {
      debugPrint(
          'Auth state changed: ${user != null ? "User logged in: ${user.email}" : "User logged out"}');

      try {
        TFullScreenLoader.stopLoading();

        if (Get.isDialogOpen ?? false) {
          Get.back();
        }

        final context = Get.overlayContext;
        if (context != null) {
          while (Navigator.of(context, rootNavigator: true).canPop()) {
            Navigator.of(context, rootNavigator: true).pop();
          }
        }
      } catch (e) {
        debugPrint('Error dismissing loaders: $e');
      }
    });
  }

  User? get firebaseUser => _firebaseUser.value;
  String get getUserID => _firebaseUser.value?.uid ?? "";
  String get getUserEmail => _auth.currentUser?.email ?? '';
  String get getDisplayName => _firebaseUser.value?.displayName ?? "";
  String get getPhoneNo => _firebaseUser.value?.phoneNumber ?? "";

  Stream<User?> get authStateChanges => _auth.authStateChanges();

  Future<void> logout(BuildContext context) async {
    try {
      if (Get.isRegistered<CartController>()) {
        Get.find<CartController>().clearCart();
      }
      if (Get.isRegistered<FavouritesController>()) {
        Get.find<FavouritesController>().clearFavorites();
      }
      await _auth.signOut();
      try {
        await GoogleSignIn().signOut();
      } catch (_) {}

      if (Get.isRegistered<LoginController>()) {
        Get.delete<LoginController>();
      }
      if (Get.isRegistered<UserController>()) {
        Get.find<UserController>().clearLocalStorageData();
        Get.delete<UserController>();
      }

      html.window.location.href = '/';
    } catch (e) {
      throw 'Error logging out. Please try again.';
    }
  }

  bool isUserLoggedIn() => _auth.currentUser != null;
  bool isEmailVerified() => _auth.currentUser?.emailVerified ?? false;

  Future<void> deleteAccount() async {
    try {
      await _auth.currentUser?.delete();
    } catch (e) {
      throw 'Failed to delete account: ${e.toString()}';
    }
  }

  Future<void> reAuthenticateWithEmailAndPassword(
      String email, String password) async {
    try {
      final credential =
          EmailAuthProvider.credential(email: email, password: password);
      await _auth.currentUser?.reauthenticateWithCredential(credential);
    } catch (e) {
      throw 'Re-authentication failed: ${e.toString()}';
    }
  }

  Future<UserCredential> loginWithEmailAndPassword(
      String email, String password) async {
    debugPrint('=== Login Process Started ===');
    debugPrint('Attempting login with email: ${email.trim()}');

    if (email.isEmpty || password.isEmpty) {
      throw 'Please enter both email and password';
    }

    try {
      final userCredential = await _auth
          .signInWithEmailAndPassword(
            email: email.trim(),
            password: password.trim(),
          )
          .timeout(
            const Duration(seconds: 10),
            onTimeout: () => throw 'Authentication timeout. Please try again.',
          );

      if (userCredential.user == null) {
        throw 'Authentication failed: Invalid response';
      }

      return userCredential;
    } on FirebaseAuthException catch (e) {
      throw TFirebaseAuthException(e.code).message;
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } catch (e) {
      throw 'Authentication error: ${e.toString()}';
    }
  }

  Future<UserCredential> registerWithEmailAndPassword(
      String email, String password) async {
    try {
      return await _auth.createUserWithEmailAndPassword(
          email: email, password: password);
    } on FirebaseAuthException catch (e) {
      throw TFirebaseAuthException(e.code).message;
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on FormatException catch (_) {
      throw const TFormatException();
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong. Please try again';
    }
  }

  Future<UserCredential?> signInWithGoogle() async {
    try {
      if (kIsWeb) {
        GoogleAuthProvider googleProvider = GoogleAuthProvider();
        googleProvider
            .addScope('https://www.googleapis.com/auth/contacts.readonly');
        googleProvider.setCustomParameters({'login_hint': '<EMAIL>'});

        final userCredential =
            await FirebaseAuth.instance.signInWithPopup(googleProvider);

        _dismissAllLoaders();
        Future.delayed(const Duration(milliseconds: 500), () {
          TFullScreenLoader.forceCloseAll();
        });

        return userCredential;
      } else {
        // Mobile implementation would go here
      }
    } catch (e) {
      _dismissAllLoaders();
      if (e is FirebaseAuthException && e.code == 'popup-closed-by-user') {
        return null;
      }
      throw e.toString();
    }
    return null;
  }

  void _dismissAllLoaders() {
    TFullScreenLoader.stopLoading();
  }

  Future<void> resetPassword(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
    } on FirebaseAuthException catch (e) {
      throw TFirebaseAuthException(e.code).message;
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } catch (e) {
      throw 'Something went wrong. Please try again';
    }
  }

  Future<void> sendEmailVerification() async {
    try {
      final user = _auth.currentUser;
      if (user == null) throw 'No user is currently signed in.';
      await user.sendEmailVerification();
    } on FirebaseAuthException catch (e) {
      throw TExceptions.fromCode(e.code).message;
    } catch (e) {
      throw e.toString();
    }
  }

  Future<void> updateDisplayName(String newName) async {
    try {
      await _auth.currentUser?.updateDisplayName(newName);
    } catch (e) {
      throw 'Failed to update display name: ${e.toString()}';
    }
  }

  Future<void> screenRedirect(User? user, BuildContext context) async {
    if (user != null) {
      try {
        await user.reload();
        final freshUser = _auth.currentUser;
        if (freshUser != null && freshUser.emailVerified) {
          try {
            if (!deviceStorage.isInitialized()) {
              await deviceStorage.init(freshUser.uid);
            }
          } catch (e) {}
          if (context.mounted) context.go('/');
        } else if (freshUser != null && !freshUser.emailVerified) {
          if (context.mounted) {
            context.go('/verify-email', extra: freshUser.email);
          }
        }
      } catch (e) {
        debugPrint('Error during screen redirect: $e');
      }
    } else {
      if (context.mounted) context.go('/login');
    }
  }

  Future<void> refreshUser() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) return;
      await currentUser.reload();
      _firebaseUser.value = _auth.currentUser;
    } catch (e) {
      print('Error refreshing user: $e');
    }
  }

  Future<void> phoneAuthentication(String phoneNumber) async {
    try {
      await _auth.verifyPhoneNumber(
        phoneNumber: phoneNumber,
        verificationCompleted: (credential) async {
          await _auth.signInWithCredential(credential);
        },
        verificationFailed: (e) {
          throw e.message!;
        },
        codeSent: (String verId, int? resendToken) {
          verificationId = verId;
        },
        codeAutoRetrievalTimeout: (String verId) {
          verificationId = verId;
        },
      );
    } on FirebaseAuthException catch (e) {
      throw TFirebaseAuthException(e.code).message;
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } catch (e) {
      throw 'Something went wrong. Please try again';
    }
  }

  Future<bool> verifyOTP(String otp) async {
    try {
      var credential = PhoneAuthProvider.credential(
        verificationId: verificationId,
        smsCode: otp,
      );
      await _auth.signInWithCredential(credential);
      return true;
    } on FirebaseAuthException catch (e) {
      throw TFirebaseAuthException(e.code).message;
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } catch (e) {
      throw 'Something went wrong. Please try again';
    }
  }

  User? get authUser => FirebaseAuth.instance.currentUser;

  Future<void> clearAuthSession() async {
    try {
      final storage = TLocalStorage();
      await storage.removeData('user_data');
      await FirebaseAuth.instance.signOut();
    } catch (e) {
      throw 'Error clearing auth session: $e';
    }
  }

  /// Add new address
  Future<String> addAddress(AddressModel address, String getUserID) async {
    try {
      final docRef = await _db
          .collection('Users')
          .doc(getUserID)
          .collection('Addresses')
          .add(address.toJson());
      return docRef.id;
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong. Please try again.';
    }
  }

  Future<void> updateAddress(AddressModel address, String getUserID) async {
    try {
      await _db
          .collection('Users')
          .doc(getUserID)
          .collection('Addresses')
          .doc(address.id)
          .update(address.toJson());
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong. Please try again.';
    }
  }

  /// Fetch all user addresses
  Future<List<AddressModel>> fetchUserAddresses() async {
    try {
      final userId = FirebaseAuth.instance.currentUser!.uid;
      if (userId.isEmpty) {
        throw 'User ID is empty. Cannot fetch addresses.';
      }
      final result = await _db
          .collection('Users')
          .doc(userId)
          .collection('Addresses')
          .get();
      return result.docs
          .map((document) => AddressModel.fromDocumentSnapshot(document))
          .toList();
    } catch (e) {
      throw 'Failed to fetch user addresses: $e';
    }
  }

  /// Update selected field of an address
  Future<void> updateSelectedField(
      String userId, String addressId, bool selected) async {
    try {
      await _db
          .collection('Users')
          .doc(userId)
          .collection('Addresses')
          .doc(addressId)
          .update({'selectedAddress': selected});
    } catch (e) {
      throw 'Failed to update address selection: $e';
    }
  }

  /// Delete User Address by Firestore Document ID
  Future<void> deleteUserAddress(String id) async {
    try {
      await _db
          .collection('Users')
          .doc(FirebaseAuth.instance.currentUser!.uid)
          .collection('Addresses')
          .doc(id)
          .delete();
    } catch (e) {
      throw Exception('Failed to delete address: $e');
    }
  }
}
