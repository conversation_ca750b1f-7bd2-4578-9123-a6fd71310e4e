import 'package:flutter/material.dart';
import 'package:iconsax/iconsax.dart';
import 'package:demarth/utils/constants/ghana_regions.dart';
import 'package:demarth/features/shop/widgets/document_upload_tile.dart';
import 'package:demarth/common/widgets/layouts/profile_layout.dart';

class CreateShopForm extends StatefulWidget {
  final bool isFirebaseInitialized;
  final GlobalKey<FormState> formKey;
  final bool isMobile;
  final bool isLoading;
  final bool isLoadingFee;
  final double listingFee;

  // Controllers
  final TextEditingController shopNameController;
  final TextEditingController shopDescriptionController;
  final TextEditingController businessPhoneController;
  final TextEditingController businessEmailController;
  final TextEditingController businessLocationController;
  final TextEditingController ghanaCardController;
  final TextEditingController bankAccountNameController;
  final TextEditingController bankAccountNumberController;
  final TextEditingController mobileMoneyNumberController;
  final TextEditingController mobileMoneyNameController;

  // Selected Values
  final String? selectedBusinessType;
  final String? selectedRegion;
  final String? selectedArea;
  final String? selectedBank;
  final String? selectedMobileMoneyProvider;

  // Lists
  final List<String> businessTypes;
  final List<String> ghanaianBanks;
  final List<String> mobileMoneyProviders;
  final List<String> filteredAreas;

  // URLs
  final String? ghanaCardFrontUrl;
  final String? ghanaCardBackUrl;
  final String? businessCertificateUrl;
  final List<String> businessDocumentUrls;
  final List<String> businessDocumentsPreview;

  // Loading States
  final bool isLoadingGhanaCardFront;
  final bool isLoadingGhanaCardBack;
  final bool isLoadingBusinessCertificate;
  final bool isLoadingBusinessDocuments;

  // Callbacks
  final Function(String?) onBusinessTypeChanged;
  final Function(String?) onRegionChanged;
  final Function(String?) onAreaChanged;
  final Function(String?) onBankChanged;
  final Function(String?) onMobileMoneyProviderChanged;
  final VoidCallback onCreateShopPressed;
  final Function(bool) onPickGhanaCardImage;
  final VoidCallback onPickBusinessCertificate;
  final VoidCallback onPickBusinessDocument;

  final String selectedCountryCode;
  final VoidCallback onCountryCodeTap;

  const CreateShopForm({
    super.key,
    required this.isFirebaseInitialized,
    required this.formKey,
    required this.isMobile,
    required this.isLoading,
    required this.isLoadingFee,
    required this.listingFee,
    required this.shopNameController,
    required this.shopDescriptionController,
    required this.businessPhoneController,
    required this.businessEmailController,
    required this.businessLocationController,
    required this.ghanaCardController,
    required this.bankAccountNameController,
    required this.bankAccountNumberController,
    required this.mobileMoneyNumberController,
    required this.mobileMoneyNameController,
    required this.selectedBusinessType,
    required this.selectedRegion,
    required this.selectedArea,
    required this.selectedBank,
    required this.selectedMobileMoneyProvider,
    required this.businessTypes,
    required this.ghanaianBanks,
    required this.mobileMoneyProviders,
    required this.filteredAreas,
    required this.ghanaCardFrontUrl,
    required this.ghanaCardBackUrl,
    required this.businessCertificateUrl,
    required this.businessDocumentUrls,
    required this.businessDocumentsPreview,
    required this.isLoadingGhanaCardFront,
    required this.isLoadingGhanaCardBack,
    required this.isLoadingBusinessCertificate,
    required this.isLoadingBusinessDocuments,
    required this.onBusinessTypeChanged,
    required this.onRegionChanged,
    required this.onAreaChanged,
    required this.onBankChanged,
    required this.onMobileMoneyProviderChanged,
    required this.onCreateShopPressed,
    required this.onPickGhanaCardImage,
    required this.onPickBusinessCertificate,
    required this.onPickBusinessDocument,
    required this.selectedCountryCode,
    required this.onCountryCodeTap,
  });

  @override
  State<CreateShopForm> createState() => _CreateShopFormState();
}

class _CreateShopFormState extends State<CreateShopForm> {
  int _currentStep = 0;

  void _handleStepContinue() {
    if (_currentStep < 3) {
      setState(() => _currentStep++);
    } else {
      widget.onCreateShopPressed();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isFirebaseInitialized) {
      return const Center(child: CircularProgressIndicator());
    }

    return ProfileLayout(
      title: 'Create Your Shop',
      currentRoute: '/shop/create',
      child: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFFFAFBFC),
              Colors.white,
            ],
          ),
        ),
        child: SingleChildScrollView(
          child: Column(
            children: [
              // Sleek minimal header
              Container(
                width: double.infinity,
                padding: const EdgeInsets.fromLTRB(40, 80, 40, 60),
                child: Column(
                  children: [
                    // Minimalist icon with subtle animation
                    Container(
                      width: 72,
                      height: 72,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: Theme.of(context)
                                .primaryColor
                                .withValues(alpha: 0.1),
                            blurRadius: 20,
                            offset: const Offset(0, 8),
                          ),
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.05),
                            blurRadius: 10,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Icon(
                        Iconsax.shop,
                        size: 32,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                    const SizedBox(height: 40),
                    // Ultra-clean typography
                    const Text(
                      'Create Your Shop',
                      style: TextStyle(
                        fontSize: 42,
                        fontWeight: FontWeight.w800,
                        color: Color(0xFF1A1D29),
                        letterSpacing: -1.5,
                        height: 1.1,
                      ),
                    ),
                    const SizedBox(height: 20),
                    Container(
                      constraints: const BoxConstraints(maxWidth: 480),
                      child: const Text(
                        'Set up your business in minutes with our streamlined process',
                        style: TextStyle(
                          fontSize: 18,
                          color: Color(0xFF6B7280),
                          height: 1.6,
                          fontWeight: FontWeight.w400,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),
              // Ultra-modern progress indicator
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 40),
                padding: const EdgeInsets.all(40),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(24),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.03),
                      blurRadius: 20,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: _ModernStepper(
                  currentStep: _currentStep,
                  onStepTapped: (step) => setState(() => _currentStep = step),
                  steps: const [
                    'Basic Info',
                    'Contact',
                    'Documents',
                    'Payment',
                  ],
                ),
              ),

              const SizedBox(height: 40),
              // Sleek form container
              Container(
                margin: const EdgeInsets.fromLTRB(40, 0, 40, 80),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(28),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.04),
                      blurRadius: 30,
                      offset: const Offset(0, 8),
                    ),
                  ],
                ),
                child: Form(
                  key: widget.formKey,
                  child: AnimatedSwitcher(
                    duration: const Duration(milliseconds: 400),
                    transitionBuilder: (child, animation) {
                      return FadeTransition(
                        opacity: animation,
                        child: SlideTransition(
                          position: Tween<Offset>(
                            begin: const Offset(0, 0.02),
                            end: Offset.zero,
                          ).animate(CurvedAnimation(
                            parent: animation,
                            curve: Curves.easeOutQuart,
                          )),
                          child: child,
                        ),
                      );
                    },
                    child: _buildStepContent(_currentStep),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStepContent(int step) {
    switch (step) {
      case 0:
        return _buildBasicInfoStep();
      case 1:
        return _buildContactStep();
      case 2:
        return _buildDocumentsStep();
      case 3:
        return _buildPaymentStep();
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildBasicInfoStep() {
    return _ModernStepContainer(
      title: 'Basic Information',
      subtitle: 'Tell us about your business',
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildModernRow([
            Expanded(
              child: _ModernTextField(
                controller: widget.shopNameController,
                label: 'Business Name',
                hint: 'Enter your business name',
                icon: Iconsax.shop,
                validator: (value) => value == null || value.isEmpty
                    ? 'Please enter a business name'
                    : null,
              ),
            ),
            const SizedBox(width: 24),
            Expanded(
              child: _ModernDropdown<String>(
                value: widget.selectedBusinessType,
                label: 'Business Type',
                hint: 'Select business type',
                icon: Iconsax.building,
                items: widget.businessTypes
                    .map((type) => DropdownMenuItem(
                          value: type,
                          child: Text(type),
                        ))
                    .toList(),
                onChanged: widget.onBusinessTypeChanged,
                validator: (value) => value == null || value.isEmpty
                    ? 'Please select a business type'
                    : null,
              ),
            ),
          ]),
          const SizedBox(height: 24),
          _ModernTextField(
            controller: widget.shopDescriptionController,
            label: 'Business Description',
            hint: 'Describe your business and what you sell',
            icon: Iconsax.document_text,
            maxLines: 4,
            validator: (value) => value == null || value.isEmpty
                ? 'Please enter a business description'
                : null,
          ),
          const SizedBox(height: 48),
          _ModernStepNavigation(
            onNext: _handleStepContinue,
            nextLabel: 'Continue',
          ),
        ],
      ),
    );
  }

  Widget _buildContactStep() {
    return _ModernStepContainer(
      title: 'Contact Information',
      subtitle: 'How customers can reach you',
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildModernRow([
            Expanded(
              child: _ModernTextField(
                controller: widget.businessPhoneController,
                label: 'Business Phone',
                hint: 'Enter business phone number',
                icon: Iconsax.call,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a business phone number';
                  }
                  final cleanPhone = value.replaceAll(RegExp(r'[^\d]'), '');
                  if (cleanPhone.length < 9 || cleanPhone.length > 15) {
                    return 'Please enter a valid phone number';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 32),
            Expanded(
              child: _ModernTextField(
                controller: widget.businessEmailController,
                label: 'Business Email',
                hint: 'Enter business email address',
                icon: Iconsax.message,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a business email';
                  }
                  final emailRegex = RegExp(
                    r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
                  );
                  if (!emailRegex.hasMatch(value)) {
                    return 'Please enter a valid email address';
                  }
                  return null;
                },
              ),
            ),
          ]),
          const SizedBox(height: 32),
          _buildModernRow([
            Expanded(
              child: _ModernDropdown(
                value: widget.selectedRegion,
                label: 'Region',
                hint: 'Select your region',
                icon: Iconsax.location,
                items: GhanaRegions.regions
                    .map((region) =>
                        DropdownMenuItem(value: region, child: Text(region)))
                    .toList(),
                onChanged: widget.onRegionChanged,
                validator: (value) => value == null || value.isEmpty
                    ? 'Please select a region'
                    : null,
              ),
            ),
            const SizedBox(width: 32),
            Expanded(
              child: _ModernDropdown(
                value: widget.selectedArea,
                label: 'Area',
                hint: 'Select your area',
                icon: Iconsax.location,
                items: [
                  ...widget.filteredAreas.map((area) => DropdownMenuItem(
                        value: area,
                        child: Text(area),
                      )),
                  const DropdownMenuItem(
                    value: 'other',
                    child: Text('Other (Specify)'),
                  ),
                ],
                onChanged: widget.onAreaChanged,
                validator: (value) {
                  if (widget.selectedRegion != 'Other' &&
                      (value == null || value.isEmpty)) {
                    return 'Please select an area';
                  }
                  return null;
                },
              ),
            ),
          ]),
          const SizedBox(height: 32),
          _ModernTextField(
            controller: widget.businessLocationController,
            label: 'Exact Location',
            hint: 'Enter your exact location',
            icon: Iconsax.location,
            validator: (value) => value == null || value.isEmpty
                ? 'Please enter your exact location'
                : null,
          ),
          const SizedBox(height: 32),
          _ModernStepNavigation(
            onNext: _handleStepContinue,
            nextLabel: 'Continue',
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentsStep() {
    return _ModernStepContainer(
      title: 'Documentation',
      subtitle: 'Upload your identification documents',
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _ModernTextField(
            controller: widget.ghanaCardController,
            label: 'Ghana Card Number',
            hint: 'Enter your Ghana Card number',
            icon: Iconsax.card,
          ),
          const SizedBox(height: 32),
          _buildModernRow([
            Expanded(
              child: DocumentUploadTile(
                title: 'Ghana Card Front',
                subtitle: 'Upload the front of your Ghana Card',
                icon: Icons.credit_card,
                isUploaded: widget.ghanaCardFrontUrl != null,
                onTap: () => widget.onPickGhanaCardImage(true),
                isLoading: widget.isLoadingGhanaCardFront,
                ghanaCardFrontUrl: widget.ghanaCardFrontUrl,
                ghanaCardBackUrl: widget.ghanaCardBackUrl,
                businessCertificateUrl: widget.businessCertificateUrl,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: DocumentUploadTile(
                title: 'Ghana Card Back',
                subtitle: 'Upload the back of your Ghana Card',
                icon: Icons.credit_card,
                isUploaded: widget.ghanaCardBackUrl != null,
                onTap: () => widget.onPickGhanaCardImage(false),
                isLoading: widget.isLoadingGhanaCardBack,
                ghanaCardFrontUrl: widget.ghanaCardFrontUrl,
                ghanaCardBackUrl: widget.ghanaCardBackUrl,
                businessCertificateUrl: widget.businessCertificateUrl,
              ),
            ),
          ]),
          const SizedBox(height: 32),
          _buildModernRow([
            Expanded(
              child: DocumentUploadTile(
                title: 'Business Certificate',
                subtitle: 'Upload your business registration certificate',
                icon: Iconsax.document,
                isUploaded: widget.businessCertificateUrl != null,
                isOptional: true,
                onTap: widget.onPickBusinessCertificate,
                isLoading: widget.isLoadingBusinessCertificate,
                ghanaCardFrontUrl: widget.ghanaCardFrontUrl,
                ghanaCardBackUrl: widget.ghanaCardBackUrl,
                businessCertificateUrl: widget.businessCertificateUrl,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: DocumentUploadTile(
                title: 'Additional Documents',
                subtitle: 'Upload additional documents',
                icon: Iconsax.document_upload,
                isUploaded: widget.businessDocumentUrls.isNotEmpty,
                isOptional: true,
                onTap: widget.onPickBusinessDocument,
                isLoading: widget.isLoadingBusinessDocuments,
                ghanaCardFrontUrl: widget.ghanaCardFrontUrl,
                ghanaCardBackUrl: widget.ghanaCardBackUrl,
                businessCertificateUrl: widget.businessCertificateUrl,
                previewUrls: widget.businessDocumentsPreview,
              ),
            ),
          ]),
          const SizedBox(height: 32),
          _ModernStepNavigation(
            onNext: _handleStepContinue,
            nextLabel: 'Continue',
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentStep() {
    return _ModernStepContainer(
      title: 'Payment Information',
      subtitle: 'Set up your payment methods',
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: const Color(0xFFF0F9FF),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: const Color(0xFFBAE6FD)),
            ),
            child: Row(
              children: [
                Icon(Iconsax.info_circle,
                    color: Theme.of(context).primaryColor, size: 24),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Please complete either Bank Account Details OR Mobile Money Details OR Both',
                    style: TextStyle(
                      color: Theme.of(context).primaryColor,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 32),
          _buildModernRow([
            // Bank Account
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Bank Account Details',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w700,
                      color: Color(0xFF1A1D29),
                    ),
                  ),
                  const SizedBox(height: 24),
                  _ModernTextField(
                    controller: widget.bankAccountNameController,
                    label: 'Account Name',
                    hint: 'Enter bank account name',
                    icon: Iconsax.profile_2user,
                  ),
                  const SizedBox(height: 20),
                  _ModernTextField(
                    controller: widget.bankAccountNumberController,
                    label: 'Account Number',
                    hint: 'Enter bank account number',
                    icon: Iconsax.card,
                  ),
                  const SizedBox(height: 20),
                  _ModernDropdown(
                    value: widget.selectedBank,
                    label: 'Bank Name',
                    hint: 'Select your bank',
                    icon: Iconsax.bank,
                    items: widget.ghanaianBanks
                        .map((bank) => DropdownMenuItem<String>(
                              value: bank,
                              child: Text(bank),
                            ))
                        .toList(),
                    onChanged: widget.onBankChanged,
                  ),
                ],
              ),
            ),
            const SizedBox(width: 32),
            // Mobile Money
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Mobile Money Details',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w700,
                      color: Color(0xFF1A1D29),
                    ),
                  ),
                  const SizedBox(height: 24),
                  _ModernDropdown(
                    value: widget.selectedMobileMoneyProvider,
                    label: 'Mobile Money Provider',
                    hint: 'Select provider',
                    icon: Iconsax.mobile,
                    items: widget.mobileMoneyProviders
                        .map((provider) => DropdownMenuItem<String>(
                              value: provider,
                              child: Text(provider),
                            ))
                        .toList(),
                    onChanged: widget.onMobileMoneyProviderChanged,
                  ),
                  const SizedBox(height: 20),
                  _ModernTextField(
                    controller: widget.mobileMoneyNumberController,
                    label: 'Mobile Money Number',
                    hint: 'Enter mobile money number',
                    icon: Iconsax.mobile,
                  ),
                  const SizedBox(height: 20),
                  _ModernTextField(
                    controller: widget.mobileMoneyNameController,
                    label: 'Mobile Money Account Name',
                    hint: 'Enter account name',
                    icon: Iconsax.profile_2user,
                  ),
                ],
              ),
            ),
          ]),
          const SizedBox(height: 32),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: const Color(0xFFF0F9FF),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: const Color(0xFFBAE6FD)),
            ),
            child: Row(
              children: [
                Icon(Iconsax.info_circle,
                    color: Theme.of(context).primaryColor, size: 24),
                const SizedBox(width: 12),
                Expanded(
                  child: widget.isLoadingFee
                      ? Text(
                          'Loading fee information...',
                          style: TextStyle(
                            color: Theme.of(context).primaryColor,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        )
                      : RichText(
                          text: TextSpan(
                            style: TextStyle(
                              color: Theme.of(context).primaryColor,
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                            children: [
                              const TextSpan(
                                text: 'A shop listing fee of ',
                              ),
                              TextSpan(
                                text:
                                    'GHS ${widget.listingFee.toStringAsFixed(2)}',
                                style: const TextStyle(
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                              const TextSpan(
                                text:
                                    ' will be charged when you create your shop.',
                              ),
                            ],
                          ),
                        ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 32),
          _ModernStepNavigation(
            onNext: _handleStepContinue,
            nextLabel: 'Create Shop',
            isLoading: widget.isLoading,
          ),
        ],
      ),
    );
  }
}

// --- Helper Widgets ---

class _ModernStepper extends StatelessWidget {
  final int currentStep;
  final List<String> steps;
  final ValueChanged<int> onStepTapped;

  const _ModernStepper({
    required this.currentStep,
    required this.steps,
    required this.onStepTapped,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: List.generate(steps.length, (index) {
        final isCompleted = index < currentStep;
        final isCurrent = index == currentStep;

        return Expanded(
          child: GestureDetector(
            onTap: () => onStepTapped(index),
            child: Column(
              children: [
                Row(
                  children: [
                    // Ultra-modern step indicator
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: isCompleted
                            ? Theme.of(context).primaryColor
                            : isCurrent
                                ? Theme.of(context).primaryColor
                                : const Color(0xFFF3F4F6),
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: isCurrent
                            ? [
                                BoxShadow(
                                  color: Theme.of(context)
                                      .primaryColor
                                      .withValues(alpha: 0.3),
                                  blurRadius: 8,
                                  offset: const Offset(0, 2),
                                ),
                              ]
                            : null,
                      ),
                      child: Center(
                        child: isCompleted
                            ? const Icon(
                                Icons.check,
                                color: Colors.white,
                                size: 18,
                              )
                            : Text(
                                '${index + 1}',
                                style: TextStyle(
                                  color: isCurrent
                                      ? Colors.white
                                      : const Color(0xFF9CA3AF),
                                  fontWeight: FontWeight.w600,
                                  fontSize: 14,
                                ),
                              ),
                      ),
                    ),
                    // Sleek connection line
                    if (index < steps.length - 1)
                      Expanded(
                        child: Container(
                          height: 2,
                          margin: const EdgeInsets.symmetric(horizontal: 16),
                          decoration: BoxDecoration(
                            color: isCompleted
                                ? Theme.of(context).primaryColor
                                : const Color(0xFFE5E7EB),
                            borderRadius: BorderRadius.circular(1),
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 16),
                // Clean step label
                Text(
                  steps[index],
                  style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                    color: isCurrent
                        ? Theme.of(context).primaryColor
                        : isCompleted
                            ? const Color(0xFF374151)
                            : const Color(0xFF9CA3AF),
                    letterSpacing: 0.1,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      }),
    );
  }
}

// Modern UI Components
class _ModernStepContainer extends StatelessWidget {
  final String title;
  final String subtitle;
  final Widget child;

  const _ModernStepContainer({
    required this.title,
    required this.subtitle,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(48),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Step header
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.w700,
                  color: Color(0xFF1A1D29),
                  letterSpacing: -0.8,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                subtitle,
                style: const TextStyle(
                  fontSize: 16,
                  color: Color(0xFF6B7280),
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ),
          const SizedBox(height: 40),
          child,
        ],
      ),
    );
  }
}

class _ModernTextField extends StatelessWidget {
  final TextEditingController controller;
  final String label;
  final String hint;
  final IconData icon;
  final String? Function(String?)? validator;
  final int maxLines;

  const _ModernTextField({
    required this.controller,
    required this.label,
    required this.hint,
    required this.icon,
    this.validator,
    this.maxLines = 1,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Color(0xFF374151),
            letterSpacing: 0.1,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          validator: validator,
          maxLines: maxLines,
          style: const TextStyle(
            fontSize: 16,
            color: Color(0xFF1A1D29),
            fontWeight: FontWeight.w500,
          ),
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: const TextStyle(
              color: Color(0xFF9CA3AF),
              fontWeight: FontWeight.w400,
            ),
            prefixIcon: Icon(
              icon,
              color: const Color(0xFF6B7280),
              size: 20,
            ),
            filled: true,
            fillColor: const Color(0xFFF9FAFB),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: const BorderSide(
                color: Color(0xFFE5E7EB),
                width: 1,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: const BorderSide(
                color: Color(0xFFE5E7EB),
                width: 1,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: BorderSide(
                color: Theme.of(context).primaryColor,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: const BorderSide(
                color: Color(0xFFEF4444),
                width: 2,
              ),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: const BorderSide(
                color: Color(0xFFEF4444),
                width: 2,
              ),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
          ),
        ),
      ],
    );
  }
}

class _ModernDropdown<T> extends StatelessWidget {
  final T? value;
  final String label;
  final String hint;
  final IconData icon;
  final List<DropdownMenuItem<T>> items;
  final ValueChanged<T?>? onChanged;
  final String? Function(T?)? validator;

  const _ModernDropdown({
    required this.value,
    required this.label,
    required this.hint,
    required this.icon,
    required this.items,
    required this.onChanged,
    this.validator,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Color(0xFF374151),
            letterSpacing: 0.1,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<T>(
          value: value,
          validator: validator,
          onChanged: onChanged,
          items: items,
          style: const TextStyle(
            fontSize: 16,
            color: Color(0xFF1A1D29),
            fontWeight: FontWeight.w500,
          ),
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: const TextStyle(
              color: Color(0xFF9CA3AF),
              fontWeight: FontWeight.w400,
            ),
            prefixIcon: Icon(
              icon,
              color: const Color(0xFF6B7280),
              size: 20,
            ),
            filled: true,
            fillColor: const Color(0xFFF9FAFB),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: const BorderSide(
                color: Color(0xFFE5E7EB),
                width: 1,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: const BorderSide(
                color: Color(0xFFE5E7EB),
                width: 1,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: BorderSide(
                color: Theme.of(context).primaryColor,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: const BorderSide(
                color: Color(0xFFEF4444),
                width: 2,
              ),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: const BorderSide(
                color: Color(0xFFEF4444),
                width: 2,
              ),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
          ),
        ),
      ],
    );
  }
}

class _ModernStepNavigation extends StatelessWidget {
  final VoidCallback onNext;
  final String nextLabel;
  final bool isLoading;

  const _ModernStepNavigation({
    required this.onNext,
    required this.nextLabel,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: isLoading ? null : onNext,
        style: ElevatedButton.styleFrom(
          backgroundColor: Theme.of(context).primaryColor,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 0,
        ),
        child: isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Text(
                nextLabel,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
      ),
    );
  }
}

// Helper method for responsive rows
Widget _buildModernRow(List<Widget> children) {
  return LayoutBuilder(
    builder: (context, constraints) {
      if (constraints.maxWidth > 600) {
        return Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: children,
        );
      } else {
        return Column(
          children: children.where((child) => child is! SizedBox).toList(),
        );
      }
    },
  );
}
