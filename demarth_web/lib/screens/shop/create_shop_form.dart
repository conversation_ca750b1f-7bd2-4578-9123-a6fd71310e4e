import 'package:flutter/material.dart';
import 'package:iconsax/iconsax.dart';
import 'package:demarth/utils/constants/sizes.dart';
import 'package:demarth/utils/constants/ghana_regions.dart';
import 'package:demarth/features/shop/widgets/document_upload_tile.dart';
import 'package:demarth/common/widgets/layouts/profile_layout.dart';

class CreateShopForm extends StatefulWidget {
  final bool isFirebaseInitialized;
  final GlobalKey<FormState> formKey;
  final bool isMobile;
  final bool isLoading;
  final bool isLoadingFee;
  final double listingFee;

  // Controllers
  final TextEditingController shopNameController;
  final TextEditingController shopDescriptionController;
  final TextEditingController businessPhoneController;
  final TextEditingController businessEmailController;
  final TextEditingController businessLocationController;
  final TextEditingController ghanaCardController;
  final TextEditingController bankAccountNameController;
  final TextEditingController bankAccountNumberController;
  final TextEditingController mobileMoneyNumberController;
  final TextEditingController mobileMoneyNameController;

  // Selected Values
  final String? selectedBusinessType;
  final String? selectedRegion;
  final String? selectedArea;
  final String? selectedBank;
  final String? selectedMobileMoneyProvider;

  // Lists
  final List<String> businessTypes;
  final List<String> ghanaianBanks;
  final List<String> mobileMoneyProviders;
  final List<String> filteredAreas;

  // URLs
  final String? ghanaCardFrontUrl;
  final String? ghanaCardBackUrl;
  final String? businessCertificateUrl;
  final List<String> businessDocumentUrls;
  final List<String> businessDocumentsPreview;

  // Loading States
  final bool isLoadingGhanaCardFront;
  final bool isLoadingGhanaCardBack;
  final bool isLoadingBusinessCertificate;
  final bool isLoadingBusinessDocuments;

  // Callbacks
  final Function(String?) onBusinessTypeChanged;
  final Function(String?) onRegionChanged;
  final Function(String?) onAreaChanged;
  final Function(String?) onBankChanged;
  final Function(String?) onMobileMoneyProviderChanged;
  final VoidCallback onCreateShopPressed;
  final Function(bool) onPickGhanaCardImage;
  final VoidCallback onPickBusinessCertificate;
  final VoidCallback onPickBusinessDocument;

  final String selectedCountryCode;
  final VoidCallback onCountryCodeTap;

  const CreateShopForm({
    super.key,
    required this.isFirebaseInitialized,
    required this.formKey,
    required this.isMobile,
    required this.isLoading,
    required this.isLoadingFee,
    required this.listingFee,
    required this.shopNameController,
    required this.shopDescriptionController,
    required this.businessPhoneController,
    required this.businessEmailController,
    required this.businessLocationController,
    required this.ghanaCardController,
    required this.bankAccountNameController,
    required this.bankAccountNumberController,
    required this.mobileMoneyNumberController,
    required this.mobileMoneyNameController,
    required this.selectedBusinessType,
    required this.selectedRegion,
    required this.selectedArea,
    required this.selectedBank,
    required this.selectedMobileMoneyProvider,
    required this.businessTypes,
    required this.ghanaianBanks,
    required this.mobileMoneyProviders,
    required this.filteredAreas,
    required this.ghanaCardFrontUrl,
    required this.ghanaCardBackUrl,
    required this.businessCertificateUrl,
    required this.businessDocumentUrls,
    required this.businessDocumentsPreview,
    required this.isLoadingGhanaCardFront,
    required this.isLoadingGhanaCardBack,
    required this.isLoadingBusinessCertificate,
    required this.isLoadingBusinessDocuments,
    required this.onBusinessTypeChanged,
    required this.onRegionChanged,
    required this.onAreaChanged,
    required this.onBankChanged,
    required this.onMobileMoneyProviderChanged,
    required this.onCreateShopPressed,
    required this.onPickGhanaCardImage,
    required this.onPickBusinessCertificate,
    required this.onPickBusinessDocument,
    required this.selectedCountryCode,
    required this.onCountryCodeTap,
  });

  @override
  State<CreateShopForm> createState() => _CreateShopFormState();
}

class _CreateShopFormState extends State<CreateShopForm> {
  int _currentStep = 0;

  void _handleStepContinue() {
    if (_currentStep < 3) {
      setState(() => _currentStep++);
    } else {
      widget.onCreateShopPressed();
    }
  }

  void _handleStepCancel() {
    if (_currentStep > 0) {
      setState(() => _currentStep--);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isFirebaseInitialized) {
      return const Center(child: CircularProgressIndicator());
    }

    // Professional layout with enhanced design and proper constraints
    return ProfileLayout(
      title: 'Create Your Shop',
      currentRoute: '/shop/create',
      child: SizedBox(
        width: double.infinity,
        height: MediaQuery.of(context).size.height,
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.grey.shade50,
                Colors.white,
              ],
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              // Professional header with modern design
              Container(
                width: double.infinity,
                padding:
                    const EdgeInsets.symmetric(vertical: 48, horizontal: 32),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Theme.of(context).primaryColor,
                      Theme.of(context).primaryColor.withValues(alpha: 0.8),
                      Theme.of(context).primaryColor.withValues(alpha: 0.9),
                    ],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color:
                          Theme.of(context).primaryColor.withValues(alpha: 0.3),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: Colors.white.withValues(alpha: 0.3),
                          width: 2,
                        ),
                      ),
                      child: const Icon(
                        Iconsax.shop,
                        size: 48,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 24),
                    Text(
                      'Create Your Shop',
                      style:
                          Theme.of(context).textTheme.headlineLarge?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                letterSpacing: -0.5,
                              ),
                    ),
                    const SizedBox(height: 12),
                    Container(
                      constraints: const BoxConstraints(maxWidth: 600),
                      child: Text(
                        'Join our marketplace and start selling your products to thousands of customers',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                              color: Colors.white.withValues(alpha: 0.9),
                              fontSize: 16,
                              height: 1.5,
                            ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),
              // Professional stepper with enhanced design
              Container(
                width: double.infinity,
                padding: const EdgeInsets.fromLTRB(32, 40, 32, 24),
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: _HorizontalStepper(
                  currentStep: _currentStep,
                  onStepTapped: (step) => setState(() => _currentStep = step),
                  steps: const [
                    'Basic Info',
                    'Contact',
                    'Documents',
                    'Payment',
                  ],
                ),
              ),
              // Professional form content area
              Expanded(
                child: Container(
                  width: double.infinity,
                  margin: const EdgeInsets.all(32),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.08),
                        blurRadius: 20,
                        offset: const Offset(0, 4),
                      ),
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.04),
                        blurRadius: 40,
                        offset: const Offset(0, 8),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(20),
                    child: Form(
                      key: widget.formKey,
                      child: Container(
                        constraints: const BoxConstraints(
                          minHeight: 400,
                        ),
                        child: AnimatedSwitcher(
                          duration: const Duration(milliseconds: 500),
                          transitionBuilder: (child, animation) {
                            return SlideTransition(
                              position: Tween<Offset>(
                                begin: const Offset(0.05, 0),
                                end: Offset.zero,
                              ).animate(CurvedAnimation(
                                parent: animation,
                                curve: Curves.easeOutCubic,
                              )),
                              child: FadeTransition(
                                opacity: CurvedAnimation(
                                  parent: animation,
                                  curve: Curves.easeInOut,
                                ),
                                child: child,
                              ),
                            );
                          },
                          child: _buildStepContent(_currentStep),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              // Professional navigation buttons
              Container(
                width: double.infinity,
                padding: const EdgeInsets.fromLTRB(32, 24, 32, 32),
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border(
                    top: BorderSide(
                      color: Colors.grey.shade100,
                      width: 1,
                    ),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 10,
                      offset: const Offset(0, -2),
                    ),
                  ],
                ),
                child: _StepNavigationButtons(
                  currentStep: _currentStep,
                  totalSteps: 4,
                  onBack: _handleStepCancel,
                  onContinue: _handleStepContinue,
                  isLoading: widget.isLoading,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStepContent(int step) {
    switch (step) {
      case 0:
        return _buildBasicInfoStep();
      case 1:
        return _buildContactStep();
      case 2:
        return _buildDocumentsStep();
      case 3:
        return _buildPaymentStep();
      default:
        return const SizedBox.shrink();
    }
  }

  // Professional input decoration helper
  InputDecoration _buildInputDecoration({
    required String labelText,
    required String hintText,
    required IconData prefixIcon,
    String? prefixText,
    Widget? suffixIcon,
  }) {
    return InputDecoration(
      labelText: labelText,
      hintText: hintText,
      prefixText: prefixText,
      prefixIcon: Container(
        margin: const EdgeInsets.only(right: 16),
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          prefixIcon,
          size: 20,
          color: Theme.of(context).primaryColor,
        ),
      ),
      suffixIcon: suffixIcon,
      labelStyle: TextStyle(
        color: Colors.grey.shade700,
        fontWeight: FontWeight.w600,
        fontSize: 16,
      ),
      hintStyle: TextStyle(
        color: Colors.grey.shade500,
        fontSize: 15,
      ),
      filled: true,
      fillColor: Colors.white,
      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 18),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(16),
        borderSide: BorderSide(color: Colors.grey.shade200, width: 1.5),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(16),
        borderSide: BorderSide(color: Colors.grey.shade200, width: 1.5),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(16),
        borderSide: BorderSide(
          color: Theme.of(context).primaryColor,
          width: 2.5,
        ),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: Colors.red, width: 1),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: Colors.red, width: 2),
      ),
    );
  }

  Widget _buildBasicInfoStep() {
    return _StepCard(
      title: 'Basic Information',
      children: [
        _responsiveRow([
          Expanded(
            child: TextFormField(
              controller: widget.shopNameController,
              decoration: _buildInputDecoration(
                labelText: 'Business Name',
                hintText: 'Enter your business name',
                prefixIcon: Iconsax.shop,
              ),
              validator: (value) => value == null || value.isEmpty
                  ? 'Please enter a business name'
                  : null,
            ),
          ),
          const SizedBox(width: 24),
          Expanded(
            child: DropdownButtonFormField<String>(
              value: widget.selectedBusinessType,
              decoration: _buildInputDecoration(
                labelText: 'Business Type',
                hintText: 'Select business type',
                prefixIcon: Iconsax.building,
              ),
              items: widget.businessTypes
                  .map((type) =>
                      DropdownMenuItem(value: type, child: Text(type)))
                  .toList(),
              onChanged: widget.onBusinessTypeChanged,
              validator: (value) => value == null || value.isEmpty
                  ? 'Please select a business type'
                  : null,
            ),
          ),
        ]),
        const SizedBox(height: TSizes.spaceBtwInputFields),
        TextFormField(
          controller: widget.shopDescriptionController,
          maxLines: 3,
          decoration: _buildInputDecoration(
            labelText: 'Business Description',
            hintText: 'Describe your business',
            prefixIcon: Iconsax.document_text,
          ),
          validator: (value) => value == null || value.isEmpty
              ? 'Please enter a business description'
              : null,
        ),
        const SizedBox(height: 32),
        _StepControls(
          isFirst: true,
          isLast: false,
          onContinue: _handleStepContinue,
          onCancel: _handleStepCancel,
        ),
      ],
    );
  }

  Widget _buildContactStep() {
    return _StepCard(
      title: 'Contact Information',
      children: [
        _responsiveRow([
          Expanded(
            child: TextFormField(
              controller: widget.businessPhoneController,
              keyboardType: TextInputType.phone,
              decoration: InputDecoration(
                labelText: 'Business Phone',
                hintText: 'Enter business phone number',
                prefixIcon: Icon(Iconsax.call, color: Colors.grey.shade600),
                prefixText: '${widget.selectedCountryCode} ',
                // hintStyle: _hintStyle, // Removed
                suffixIcon: IconButton(
                  icon: const Icon(Icons.arrow_drop_down),
                  onPressed: widget.onCountryCodeTap,
                ),
                border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(TSizes.borderRadiusSm)),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
                  borderSide: BorderSide(
                      color: Theme.of(context).primaryColor, width: 2),
                ),
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
                  borderSide: const BorderSide(color: Colors.red, width: 2),
                ),
                focusedErrorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
                  borderSide: const BorderSide(color: Colors.red, width: 2),
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a business phone number';
                }
                final cleanPhone = value.replaceAll(RegExp(r'[^\d]'), '');
                if (cleanPhone.length < 9 || cleanPhone.length > 15) {
                  return 'Please enter a valid phone number';
                }
                return null;
              },
            ),
          ),
          const SizedBox(width: 24),
          Expanded(
            child: TextFormField(
              controller: widget.businessEmailController,
              keyboardType: TextInputType.emailAddress,
              decoration: InputDecoration(
                labelText: 'Business Email',
                hintText: 'Enter business email address',
                prefixIcon: Icon(Iconsax.message, color: Colors.grey.shade600),
                // hintStyle: _hintStyle, // Removed
                border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(TSizes.borderRadiusSm)),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
                  borderSide: BorderSide(
                      color: Theme.of(context).primaryColor, width: 2),
                ),
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
                  borderSide: const BorderSide(color: Colors.red, width: 2),
                ),
                focusedErrorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
                  borderSide: const BorderSide(color: Colors.red, width: 2),
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a business email';
                }
                final emailRegex = RegExp(
                  r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
                );
                if (!emailRegex.hasMatch(value)) {
                  return 'Please enter a valid email address';
                }
                return null;
              },
            ),
          ),
        ]),
        const SizedBox(height: TSizes.spaceBtwInputFields),
        _responsiveRow([
          Expanded(
            child: DropdownButtonFormField<String>(
              value: widget.selectedRegion,
              decoration: InputDecoration(
                labelText: 'Region',
                hintText: 'Select your region',
                prefixIcon: Icon(Iconsax.location, color: Colors.grey.shade600),
                // hintStyle: _hintStyle, // Removed
                border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(TSizes.borderRadiusSm)),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
                  borderSide: BorderSide(
                      color: Theme.of(context).primaryColor, width: 2),
                ),
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
                  borderSide: const BorderSide(color: Colors.red, width: 2),
                ),
                focusedErrorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
                  borderSide: const BorderSide(color: Colors.red, width: 2),
                ),
              ),
              items: GhanaRegions.regions
                  .map((region) =>
                      DropdownMenuItem(value: region, child: Text(region)))
                  .toList(),
              onChanged: widget.onRegionChanged,
              validator: (value) => value == null || value.isEmpty
                  ? 'Please select a region'
                  : null,
            ),
          ),
          const SizedBox(width: 24),
          Expanded(
            child: DropdownButtonFormField<String>(
              value: widget.selectedArea,
              decoration: InputDecoration(
                labelText: 'Area',
                hintText: 'Select your area',
                prefixIcon: Icon(Iconsax.location, color: Colors.grey.shade600),
                // hintStyle: _hintStyle, // Removed
                border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(TSizes.borderRadiusSm)),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
                  borderSide: BorderSide(
                      color: Theme.of(context).primaryColor, width: 2),
                ),
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
                  borderSide: const BorderSide(color: Colors.red, width: 2),
                ),
                focusedErrorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
                  borderSide: const BorderSide(color: Colors.red, width: 2),
                ),
              ),
              items: [
                ...widget.filteredAreas.map((area) => DropdownMenuItem(
                      value: area,
                      child: Text(area),
                    )),
                const DropdownMenuItem(
                  value: 'other',
                  child: Text('Other (Specify)'),
                ),
              ],
              onChanged: widget.onAreaChanged,
              validator: (value) {
                if (widget.selectedRegion != 'Other' &&
                    (value == null || value.isEmpty)) {
                  return 'Please select an area';
                }
                return null;
              },
            ),
          ),
        ]),
        const SizedBox(height: TSizes.spaceBtwInputFields),
        TextFormField(
          controller: widget.businessLocationController,
          decoration: InputDecoration(
            labelText: 'Exact Location',
            hintText: 'Enter your exact location',
            prefixIcon: Icon(Iconsax.location, color: Colors.grey.shade600),
            // hintStyle: _hintStyle, // Removed
            border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(TSizes.borderRadiusSm)),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
              borderSide:
                  BorderSide(color: Theme.of(context).primaryColor, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
              borderSide: const BorderSide(color: Colors.red, width: 2),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
              borderSide: const BorderSide(color: Colors.red, width: 2),
            ),
          ),
          validator: (value) => value == null || value.isEmpty
              ? 'Please enter your exact location'
              : null,
        ),
        const SizedBox(height: 32),
        _StepControls(
          isFirst: false,
          isLast: false,
          onContinue: _handleStepContinue,
          onCancel: _handleStepCancel,
        ),
      ],
    );
  }

  Widget _buildDocumentsStep() {
    return _StepCard(title: 'Documentation', children: [
      TextFormField(
        controller: widget.ghanaCardController,
        decoration: InputDecoration(
          labelText: 'Ghana Card Number',
          hintText: 'Enter your Ghana Card number',
          prefixIcon: Icon(Iconsax.card, color: Colors.grey.shade600),
          // hintStyle: _hintStyle, // Removed
          border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(TSizes.borderRadiusSm)),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
            borderSide:
                BorderSide(color: Theme.of(context).primaryColor, width: 2),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
            borderSide: const BorderSide(color: Colors.red, width: 2),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
            borderSide: const BorderSide(color: Colors.red, width: 2),
          ),
        ),
      ),
      const SizedBox(height: TSizes.spaceBtwItems),
      _responsiveRow([
        Expanded(
          child: DocumentUploadTile(
            title: 'Ghana Card Front',
            subtitle: 'Upload the front of your Ghana Card',
            icon: Icons.credit_card,
            isUploaded: widget.ghanaCardFrontUrl != null,
            onTap: () => widget.onPickGhanaCardImage(true),
            isLoading: widget.isLoadingGhanaCardFront,
            ghanaCardFrontUrl: widget.ghanaCardFrontUrl,
            ghanaCardBackUrl: widget.ghanaCardBackUrl,
            businessCertificateUrl: widget.businessCertificateUrl,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: DocumentUploadTile(
            title: 'Ghana Card Back',
            subtitle: 'Upload the back of your Ghana Card',
            icon: Icons.credit_card,
            isUploaded: widget.ghanaCardBackUrl != null,
            onTap: () => widget.onPickGhanaCardImage(false),
            isLoading: widget.isLoadingGhanaCardBack,
            ghanaCardFrontUrl: widget.ghanaCardFrontUrl,
            ghanaCardBackUrl: widget.ghanaCardBackUrl,
            businessCertificateUrl: widget.businessCertificateUrl,
          ),
        ),
      ]),
      const SizedBox(height: TSizes.spaceBtwItems),
      _responsiveRow([
        Expanded(
          child: DocumentUploadTile(
            title: 'Business Certificate',
            subtitle: 'Upload your business registration certificate',
            icon: Iconsax.document,
            isUploaded: widget.businessCertificateUrl != null,
            isOptional: true,
            onTap: widget.onPickBusinessCertificate,
            isLoading: widget.isLoadingBusinessCertificate,
            ghanaCardFrontUrl: widget.ghanaCardFrontUrl,
            ghanaCardBackUrl: widget.ghanaCardBackUrl,
            businessCertificateUrl: widget.businessCertificateUrl,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: DocumentUploadTile(
            title: 'Additional Documents',
            subtitle: 'Upload additional documents',
            icon: Iconsax.document_upload,
            isUploaded: widget.businessDocumentUrls.isNotEmpty,
            isOptional: true,
            onTap: widget.onPickBusinessDocument,
            isLoading: widget.isLoadingBusinessDocuments,
            ghanaCardFrontUrl: widget.ghanaCardFrontUrl,
            ghanaCardBackUrl: widget.ghanaCardBackUrl,
            businessCertificateUrl: widget.businessCertificateUrl,
            previewUrls: widget.businessDocumentsPreview,
          ),
        ),
      ]),
      const SizedBox(height: 32),
      _StepControls(
        isFirst: false,
        isLast: false,
        onContinue: _handleStepContinue,
        onCancel: _handleStepCancel,
      ),
    ]);
  }

  Widget _buildPaymentStep() {
    return _StepCard(
      title: 'Payment Information',
      children: [
        Container(
          width: double.infinity,
          margin: const EdgeInsets.only(bottom: TSizes.spaceBtwItems),
          padding: const EdgeInsets.all(TSizes.sm),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
            border: Border.all(color: Colors.blue.shade200),
          ),
          child: IntrinsicHeight(
            child: LayoutBuilder(
              builder: (context, constraints) {
                return Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(Iconsax.info_circle, color: Colors.blue.shade700),
                    const SizedBox(width: TSizes.xs),
                    SizedBox(
                      width: constraints.maxWidth - 24 - TSizes.xs,
                      child: Text(
                        'Please complete either Bank Account Details OR Mobile Money Details OR Both',
                        style: TextStyle(color: Colors.blue.shade700),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
        _responsiveRow([
          // Bank Account
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Bank Account Details',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: TSizes.spaceBtwItems),
                TextFormField(
                  controller: widget.bankAccountNameController,
                  decoration: InputDecoration(
                    labelText: 'Account Name',
                    hintText: 'Enter bank account name',
                    prefixIcon: Icon(Iconsax.profile_2user,
                        color: Colors.grey.shade600),
                    // hintStyle: _hintStyle, // Removed
                    border: OutlineInputBorder(
                        borderRadius:
                            BorderRadius.circular(TSizes.borderRadiusSm)),
                    enabledBorder: OutlineInputBorder(
                      borderRadius:
                          BorderRadius.circular(TSizes.borderRadiusSm),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius:
                          BorderRadius.circular(TSizes.borderRadiusSm),
                      borderSide: BorderSide(
                          color: Theme.of(context).primaryColor, width: 2),
                    ),
                    errorBorder: OutlineInputBorder(
                      borderRadius:
                          BorderRadius.circular(TSizes.borderRadiusSm),
                      borderSide: const BorderSide(color: Colors.red, width: 2),
                    ),
                    focusedErrorBorder: OutlineInputBorder(
                      borderRadius:
                          BorderRadius.circular(TSizes.borderRadiusSm),
                      borderSide: const BorderSide(color: Colors.red, width: 2),
                    ),
                  ),
                ),
                const SizedBox(height: TSizes.spaceBtwInputFields),
                TextFormField(
                  controller: widget.bankAccountNumberController,
                  decoration: InputDecoration(
                    labelText: 'Account Number',
                    hintText: 'Enter bank account number',
                    prefixIcon: Icon(Iconsax.card, color: Colors.grey.shade600),
                    // hintStyle: _hintStyle, // Removed
                    border: OutlineInputBorder(
                        borderRadius:
                            BorderRadius.circular(TSizes.borderRadiusSm)),
                    enabledBorder: OutlineInputBorder(
                      borderRadius:
                          BorderRadius.circular(TSizes.borderRadiusSm),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius:
                          BorderRadius.circular(TSizes.borderRadiusSm),
                      borderSide: BorderSide(
                          color: Theme.of(context).primaryColor, width: 2),
                    ),
                    errorBorder: OutlineInputBorder(
                      borderRadius:
                          BorderRadius.circular(TSizes.borderRadiusSm),
                      borderSide: const BorderSide(color: Colors.red, width: 2),
                    ),
                    focusedErrorBorder: OutlineInputBorder(
                      borderRadius:
                          BorderRadius.circular(TSizes.borderRadiusSm),
                      borderSide: const BorderSide(color: Colors.red, width: 2),
                    ),
                  ),
                  keyboardType: TextInputType.number,
                  maxLength: 14,
                ),
                const SizedBox(height: TSizes.spaceBtwInputFields),
                DropdownButtonFormField<String>(
                  value: widget.selectedBank,
                  decoration: InputDecoration(
                    labelText: 'Bank Name',
                    hintText: 'Select your bank',
                    prefixIcon: Icon(Iconsax.bank, color: Colors.grey.shade600),
                    // hintStyle: _hintStyle, // Removed
                    border: OutlineInputBorder(
                        borderRadius:
                            BorderRadius.circular(TSizes.borderRadiusSm)),
                    enabledBorder: OutlineInputBorder(
                      borderRadius:
                          BorderRadius.circular(TSizes.borderRadiusSm),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius:
                          BorderRadius.circular(TSizes.borderRadiusSm),
                      borderSide: BorderSide(
                          color: Theme.of(context).primaryColor, width: 2),
                    ),
                    errorBorder: OutlineInputBorder(
                      borderRadius:
                          BorderRadius.circular(TSizes.borderRadiusSm),
                      borderSide: const BorderSide(color: Colors.red, width: 2),
                    ),
                    focusedErrorBorder: OutlineInputBorder(
                      borderRadius:
                          BorderRadius.circular(TSizes.borderRadiusSm),
                      borderSide: const BorderSide(color: Colors.red, width: 2),
                    ),
                  ),
                  items: widget.ghanaianBanks
                      .map((bank) => DropdownMenuItem<String>(
                            value: bank,
                            child: Text(bank),
                          ))
                      .toList(),
                  onChanged: widget.onBankChanged,
                  isExpanded: true,
                ),
              ],
            ),
          ),
          const SizedBox(width: 24),
          // Mobile Money
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Mobile Money Details',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: TSizes.spaceBtwItems),
                DropdownButtonFormField<String>(
                  value: widget.selectedMobileMoneyProvider,
                  decoration: InputDecoration(
                    labelText: 'Mobile Money Provider',
                    hintText: 'Select provider',
                    prefixIcon:
                        Icon(Iconsax.mobile, color: Colors.grey.shade600),
                    // hintStyle: _hintStyle, // Removed
                    border: OutlineInputBorder(
                        borderRadius:
                            BorderRadius.circular(TSizes.borderRadiusSm)),
                    enabledBorder: OutlineInputBorder(
                      borderRadius:
                          BorderRadius.circular(TSizes.borderRadiusSm),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius:
                          BorderRadius.circular(TSizes.borderRadiusSm),
                      borderSide: BorderSide(
                          color: Theme.of(context).primaryColor, width: 2),
                    ),
                    errorBorder: OutlineInputBorder(
                      borderRadius:
                          BorderRadius.circular(TSizes.borderRadiusSm),
                      borderSide: const BorderSide(color: Colors.red, width: 2),
                    ),
                    focusedErrorBorder: OutlineInputBorder(
                      borderRadius:
                          BorderRadius.circular(TSizes.borderRadiusSm),
                      borderSide: const BorderSide(color: Colors.red, width: 2),
                    ),
                  ),
                  items: widget.mobileMoneyProviders
                      .map((provider) => DropdownMenuItem<String>(
                            value: provider,
                            child: Text(provider),
                          ))
                      .toList(),
                  onChanged: widget.onMobileMoneyProviderChanged,
                ),
                const SizedBox(height: TSizes.spaceBtwInputFields),
                TextFormField(
                  controller: widget.mobileMoneyNumberController,
                  decoration: InputDecoration(
                    labelText: 'Mobile Money Number',
                    hintText: 'Enter mobile money number',
                    prefixText: '+233 ',
                    prefixIcon:
                        Icon(Iconsax.mobile, color: Colors.grey.shade600),
                    // hintStyle: _hintStyle, // Removed
                    border: OutlineInputBorder(
                        borderRadius:
                            BorderRadius.circular(TSizes.borderRadiusSm)),
                    enabledBorder: OutlineInputBorder(
                      borderRadius:
                          BorderRadius.circular(TSizes.borderRadiusSm),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius:
                          BorderRadius.circular(TSizes.borderRadiusSm),
                      borderSide: BorderSide(
                          color: Theme.of(context).primaryColor, width: 2),
                    ),
                    errorBorder: OutlineInputBorder(
                      borderRadius:
                          BorderRadius.circular(TSizes.borderRadiusSm),
                      borderSide: const BorderSide(color: Colors.red, width: 2),
                    ),
                    focusedErrorBorder: OutlineInputBorder(
                      borderRadius:
                          BorderRadius.circular(TSizes.borderRadiusSm),
                      borderSide: const BorderSide(color: Colors.red, width: 2),
                    ),
                  ),
                  keyboardType: TextInputType.phone,
                  maxLength: 10,
                ),
                const SizedBox(height: TSizes.spaceBtwInputFields),
                TextFormField(
                  controller: widget.mobileMoneyNameController,
                  decoration: InputDecoration(
                    labelText: 'Mobile Money Account Name',
                    hintText: 'Enter account name',
                    prefixIcon: Icon(Iconsax.profile_2user,
                        color: Colors.grey.shade600),
                    // hintStyle: _hintStyle, // Removed
                    border: OutlineInputBorder(
                        borderRadius:
                            BorderRadius.circular(TSizes.borderRadiusSm)),
                    enabledBorder: OutlineInputBorder(
                      borderRadius:
                          BorderRadius.circular(TSizes.borderRadiusSm),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius:
                          BorderRadius.circular(TSizes.borderRadiusSm),
                      borderSide: BorderSide(
                          color: Theme.of(context).primaryColor, width: 2),
                    ),
                    errorBorder: OutlineInputBorder(
                      borderRadius:
                          BorderRadius.circular(TSizes.borderRadiusSm),
                      borderSide: const BorderSide(color: Colors.red, width: 2),
                    ),
                    focusedErrorBorder: OutlineInputBorder(
                      borderRadius:
                          BorderRadius.circular(TSizes.borderRadiusSm),
                      borderSide: const BorderSide(color: Colors.red, width: 2),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ]),
        const SizedBox(height: TSizes.spaceBtwSections),
        Container(
          padding: const EdgeInsets.all(TSizes.md),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
            border: Border.all(color: Colors.blue.shade200),
          ),
          child: LayoutBuilder(
            builder: (context, constraints) {
              return Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    Iconsax.info_circle,
                    color: Colors.blue.shade700,
                    size: 24,
                  ),
                  const SizedBox(width: TSizes.sm),
                  SizedBox(
                    width: constraints.maxWidth - 24 - TSizes.sm,
                    child: widget.isLoadingFee
                        ? const Text('Loading fee information...')
                        : RichText(
                            text: TextSpan(
                              style: Theme.of(context).textTheme.bodyMedium,
                              children: [
                                const TextSpan(
                                  text: 'A shop listing fee of ',
                                ),
                                TextSpan(
                                  text:
                                      'GHS ${widget.listingFee.toStringAsFixed(2)}',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.blue.shade700,
                                  ),
                                ),
                                const TextSpan(
                                  text:
                                      ' will be charged when you create your shop.',
                                ),
                              ],
                            ),
                          ),
                  ),
                ],
              );
            },
          ),
        ),
        const SizedBox(height: 32),
        _StepControls(
          isFirst: false,
          isLast: true,
          onContinue: _handleStepContinue,
          onCancel: _handleStepCancel,
          isLoading: widget.isLoading,
        ),
      ],
    );
  }

  /// Responsive row: horizontal on wide, vertical on narrow screens
  Widget _responsiveRow(List<Widget> children) {
    final isWide = MediaQuery.of(context).size.width > 700;
    if (isWide) {
      // Simple Row with Expanded children for wide screens
      return Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: children
            .map((child) {
              // Extract the actual child from Expanded wrapper if present
              Widget actualChild = child;
              if (child is Expanded) {
                actualChild = child.child;
              }
              return Expanded(child: actualChild);
            })
            .expand((w) => [w, const SizedBox(width: 24)])
            .toList()
          ..removeLast(),
      );
    } else {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: children
            .map((child) {
              // Extract the actual child from Expanded wrapper if present
              Widget actualChild = child;
              if (child is Expanded) {
                actualChild = child.child;
              }
              return actualChild;
            })
            .expand((w) => [w, const SizedBox(height: 16)])
            .toList()
          ..removeLast(),
      );
    }
  }
}

// --- Helper Widgets ---

class _HorizontalStepper extends StatelessWidget {
  final int currentStep;
  final List<String> steps;
  final ValueChanged<int> onStepTapped;

  const _HorizontalStepper({
    required this.currentStep,
    required this.steps,
    required this.onStepTapped,
  });

  @override
  Widget build(BuildContext context) {
    final isWide = MediaQuery.of(context).size.width > 700;
    return Container(
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.grey.shade100, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.06),
            blurRadius: 15,
            offset: const Offset(0, 4),
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 30,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: isWide ? _buildWideLayout(context) : _buildNarrowLayout(context),
    );
  }

  Widget _buildWideLayout(BuildContext context) {
    // Calculate step width based on available space
    final screenWidth = MediaQuery.of(context).size.width;
    final availableWidth = (screenWidth > 1200 ? 1200 : screenWidth) -
        112; // Container constraints minus padding
    final stepWidth = availableWidth / steps.length;

    return SizedBox(
      height: 100, // Fixed height to prevent layout issues
      child: Stack(
        children: [
          // Step items positioned absolutely to avoid flex constraints
          ...List.generate(steps.length, (index) {
            final isActive = index == currentStep;
            final isCompleted = index < currentStep;
            final leftPosition = index * stepWidth;

            return Positioned(
              left: leftPosition,
              top: 0,
              width: stepWidth,
              height: 100,
              child: InkWell(
                onTap: () => onStepTapped(index),
                borderRadius: BorderRadius.circular(12),
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          gradient: isActive || isCompleted
                              ? LinearGradient(
                                  colors: [
                                    Theme.of(context).primaryColor,
                                    Theme.of(context)
                                        .primaryColor
                                        .withValues(alpha: 0.8),
                                  ],
                                )
                              : null,
                          color: isActive || isCompleted
                              ? null
                              : Colors.grey.shade100,
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: isActive || isCompleted
                                ? Theme.of(context).primaryColor
                                : Colors.grey.shade300,
                            width: 2.5,
                          ),
                          boxShadow: isActive
                              ? [
                                  BoxShadow(
                                    color: Theme.of(context)
                                        .primaryColor
                                        .withValues(alpha: 0.4),
                                    blurRadius: 12,
                                    spreadRadius: 0,
                                    offset: const Offset(0, 4),
                                  ),
                                  BoxShadow(
                                    color: Theme.of(context)
                                        .primaryColor
                                        .withValues(alpha: 0.2),
                                    blurRadius: 20,
                                    spreadRadius: 0,
                                    offset: const Offset(0, 8),
                                  ),
                                ]
                              : isCompleted
                                  ? [
                                      BoxShadow(
                                        color: Theme.of(context)
                                            .primaryColor
                                            .withValues(alpha: 0.2),
                                        blurRadius: 8,
                                        offset: const Offset(0, 2),
                                      ),
                                    ]
                                  : null,
                        ),
                        child: isCompleted
                            ? const Icon(
                                Iconsax.tick_circle5,
                                color: Colors.white,
                                size: 24,
                              )
                            : Center(
                                child: Text(
                                  '${index + 1}',
                                  style: TextStyle(
                                    color: isActive
                                        ? Colors.white
                                        : Colors.grey.shade600,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),
                              ),
                      ),
                      const SizedBox(height: 12),
                      Text(
                        steps[index],
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontWeight:
                              isActive ? FontWeight.bold : FontWeight.w500,
                          fontSize: 14,
                          color: isActive
                              ? Theme.of(context).primaryColor
                              : isCompleted
                                  ? Theme.of(context)
                                      .primaryColor
                                      .withValues(alpha: 0.8)
                                  : Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildNarrowLayout(BuildContext context) {
    return SizedBox(
      height: 80, // Fixed height for mobile
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: List.generate(steps.length, (index) {
            final isActive = index == currentStep;
            final isCompleted = index < currentStep;
            return Container(
              width: 120, // Fixed width for each step
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              child: InkWell(
                onTap: () => onStepTapped(index),
                borderRadius: BorderRadius.circular(12),
                child: Container(
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        width: 36,
                        height: 36,
                        decoration: BoxDecoration(
                          color: isActive
                              ? Theme.of(context).primaryColor
                              : isCompleted
                                  ? Theme.of(context).primaryColor
                                  : Colors.grey.shade200,
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: isActive || isCompleted
                                ? Theme.of(context).primaryColor
                                : Colors.grey.shade300,
                            width: 2,
                          ),
                        ),
                        child: isCompleted
                            ? const Icon(
                                Iconsax.tick_circle,
                                color: Colors.white,
                                size: 18,
                              )
                            : Center(
                                child: Text(
                                  '${index + 1}',
                                  style: TextStyle(
                                    color: isActive
                                        ? Colors.white
                                        : Colors.grey.shade600,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        steps[index],
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontWeight:
                              isActive ? FontWeight.bold : FontWeight.w500,
                          fontSize: 12,
                          color: isActive
                              ? Theme.of(context).primaryColor
                              : isCompleted
                                  ? Theme.of(context)
                                      .primaryColor
                                      .withValues(alpha: 0.8)
                                  : Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          }),
        ),
      ),
    );
  }
}

class _StepCard extends StatelessWidget {
  final String title;
  final List<Widget> children;

  const _StepCard({required this.title, required this.children});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (context, constraints) {
      return Container(
        width: constraints.maxWidth,
        padding: const EdgeInsets.all(48),
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(24),
          border: Border.all(
            color: Colors.grey.shade100,
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.06),
              blurRadius: 20,
              offset: const Offset(0, 6),
              spreadRadius: 0,
            ),
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.03),
              blurRadius: 40,
              offset: const Offset(0, 12),
              spreadRadius: 0,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Theme.of(context).primaryColor.withValues(alpha: 0.1),
                        Theme.of(context).primaryColor.withValues(alpha: 0.05),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color:
                          Theme.of(context).primaryColor.withValues(alpha: 0.2),
                      width: 1,
                    ),
                  ),
                  child: Icon(
                    _getStepIcon(title),
                    color: Theme.of(context).primaryColor,
                    size: 28,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style:
                            Theme.of(context).textTheme.headlineSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.grey.shade800,
                                  fontSize: 24,
                                  letterSpacing: -0.5,
                                ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _getStepDescription(title),
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                              color: Colors.grey.shade600,
                              fontSize: 16,
                              height: 1.4,
                            ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 32),
            ...children,
          ],
        ),
      );
    });
  }

// Helper functions for step icons and descriptions
  IconData _getStepIcon(String title) {
    switch (title) {
      case 'Basic Information':
        return Iconsax.info_circle;
      case 'Contact Information':
        return Iconsax.call;
      case 'Documentation':
        return Iconsax.document;
      case 'Payment Information':
        return Iconsax.card;
      default:
        return Iconsax.info_circle;
    }
  }

  String _getStepDescription(String title) {
    switch (title) {
      case 'Basic Information':
        return 'Tell us about your business and what you sell';
      case 'Contact Information':
        return 'How customers can reach you and your location';
      case 'Documentation':
        return 'Upload required documents for verification';
      case 'Payment Information':
        return 'Set up your payment methods and complete registration';
      default:
        return '';
    }
  }
}

class _StepNavigationButtons extends StatelessWidget {
  final int currentStep;
  final int totalSteps;
  final VoidCallback onBack;
  final VoidCallback onContinue;
  final bool isLoading;

  const _StepNavigationButtons({
    required this.currentStep,
    required this.totalSteps,
    required this.onBack,
    required this.onContinue,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    final isFirst = currentStep == 0;
    final isLast = currentStep == totalSteps - 1;

    return Container(
      constraints: const BoxConstraints(maxWidth: 800),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          if (!isFirst)
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.08),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: OutlinedButton.icon(
                onPressed: isLoading ? null : onBack,
                icon: const Icon(Iconsax.arrow_left_2, size: 20),
                label: const Text('Previous'),
                style: OutlinedButton.styleFrom(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 28, vertical: 18),
                  side: BorderSide(color: Colors.grey.shade200, width: 1.5),
                  backgroundColor: Colors.white,
                  foregroundColor: Colors.grey.shade700,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  textStyle: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            )
          else
            const SizedBox.shrink(),
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).primaryColor,
                  Theme.of(context).primaryColor.withValues(alpha: 0.8),
                ],
              ),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).primaryColor.withValues(alpha: 0.4),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
                BoxShadow(
                  color: Theme.of(context).primaryColor.withValues(alpha: 0.2),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: ElevatedButton.icon(
              onPressed: isLoading ? null : onContinue,
              icon: isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2.5,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Icon(
                      isLast ? Iconsax.tick_circle : Iconsax.arrow_right_3,
                      size: 20,
                    ),
              label: Text(isLast ? 'Create Shop' : 'Continue'),
              style: ElevatedButton.styleFrom(
                padding:
                    const EdgeInsets.symmetric(horizontal: 36, vertical: 18),
                backgroundColor: Colors.transparent,
                foregroundColor: Colors.white,
                elevation: 0,
                shadowColor: Colors.transparent,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                textStyle: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 0.5,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _StepControls extends StatelessWidget {
  final bool isFirst;
  final bool isLast;
  final VoidCallback onContinue;
  final VoidCallback onCancel;
  final bool isLoading;

  const _StepControls({
    required this.isFirst,
    required this.isLast,
    required this.onContinue,
    required this.onCancel,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          if (!isFirst)
            OutlinedButton.icon(
              onPressed: isLoading ? null : onCancel,
              icon: const Icon(Iconsax.arrow_left_2),
              label: const Text('Previous'),
              style: OutlinedButton.styleFrom(
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                side: BorderSide(color: Colors.grey.shade300),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            )
          else
            const SizedBox.shrink(),
          ElevatedButton.icon(
            onPressed: isLoading ? null : onContinue,
            icon: isLoading
                ? const SizedBox(
                    width: 18,
                    height: 18,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Icon(isLast ? Iconsax.tick_circle : Iconsax.arrow_right_3),
            label: Text(isLast ? 'Create Shop' : 'Continue'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
              elevation: 2,
              shadowColor:
                  Theme.of(context).primaryColor.withValues(alpha: 0.3),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
