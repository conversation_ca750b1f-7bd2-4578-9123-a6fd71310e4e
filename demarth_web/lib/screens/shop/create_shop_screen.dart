import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:demarth/features/settings/services/global_settings_service.dart';
import 'package:demarth/features/shop/services/document_upload_service.dart';
import 'package:demarth/screens/shop/create_shop_form.dart';
import 'package:demarth/utils/services/paystack_service.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:demarth/data/repositories/authentication/authentication_repository.dart';
import 'package:image_picker/image_picker.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:demarth/utils/constants/ghana_regions.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:demarth/features/shop/controllers/shop_controller.dart';
import 'package:demarth/features/shop/models/shop_model.dart';

class CreateShopScreen extends StatefulWidget {
  const CreateShopScreen({super.key});

  @override
  State<CreateShopScreen> createState() => _CreateShopScreenState();
}

class _CreateShopScreenState extends State<CreateShopScreen> {
  final _shopController = ShopController.instance;
  bool _isFirebaseInitialized = false;
  double _listingFee = 200.0; // Updated fee amount
  bool _isLoadingFee = true;

  // Add this to your state variables
  String _selectedCountryCode = '+233';

  @override
  void initState() {
    super.initState();
    _initializeFirebase().then((_) {
      if (mounted) {
        _loadListingFee();
      }
    });
    // Check if user is logged in
    final currentUser = AuthenticationRepository.instance.firebaseUser;
    if (currentUser == null || !currentUser.emailVerified) {
      Future.microtask(() => context.go('/login'));
    }
  }

  Future<void> _initializeFirebase() async {
    try {
      await Firebase.initializeApp();
      if (mounted) {
        setState(() {
          _isFirebaseInitialized = true;
        });
      }
    } catch (e) {
      //
    }
  }

  Future<void> _loadListingFee() async {
    if (!mounted) return;

    setState(() {
      _isLoadingFee = true;
    });

    try {
      final fee = await GlobalSettingsService.getShopListingFee();

      if (mounted) {
        setState(() {
          _listingFee = fee;
          _isLoadingFee = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _listingFee = 10.0;
          _isLoadingFee = false;
        });
      }
    }
  }

  final _bankAccountNameController = TextEditingController();
  final _bankAccountNumberController = TextEditingController();
  final _bankNameController = TextEditingController();
  String? _selectedBank;

  // Add list of Ghana banks
  final List<String> _ghanaianBanks = [
    'Access Bank Ghana Plc',
    'Agricultural Development Bank of Ghana',
    'Bank of Africa Ghana Limited',
    'CAL Bank Limited',
    'Consolidated Bank Ghana Limited',
    'Ecobank Ghana Limited',
    'FBNBank Ghana Limited',
    'Fidelity Bank Ghana Limited',
    'First Atlantic Bank Limited',
    'First National Bank Ghana Limited',
    'GCB Bank Limited',
    'Ghana International Bank Plc',
    'Guaranty Trust Bank (Ghana) Limited',
    'National Investment Bank Limited',
    'OmniBSIC Bank Ghana Limited',
    'Prudential Bank Limited',
    'Republic Bank Ghana Limited',
    'Societe Generale Ghana Limited',
    'Stanbic Bank Ghana Limited',
    'Standard Chartered Bank Ghana Limited',
    'United Bank for Africa Ghana Limited',
    'Universal Merchant Bank Limited',
    'Zenith Bank Ghana Limited'
  ];
  final _mobileMoneyNumberController = TextEditingController();
  final _mobileMoneyNameController = TextEditingController();
  String? _selectedMobileMoneyProvider;

  // Add mobile money provider options
  final List<String> _mobileMoneyProviders = [
    'MTN Mobile Money',
    'Telecel Cash',
    'AirtelTigo Money',
  ];

  @override
  void dispose() {
    _searchController.dispose();
    _shopNameController.dispose();
    _shopDescriptionController.dispose();
    _businessPhoneController.dispose();
    _businessEmailController.dispose();
    _businessLocationController.dispose();
    _ghanaCardController.dispose();
    _bankAccountNameController.dispose();
    _bankAccountNumberController.dispose();
    _bankNameController.dispose();
    _mobileMoneyNumberController.dispose();
    _mobileMoneyNameController.dispose();
    super.dispose();
  }

  String? _ghanaCardFrontPreview;
  String? _ghanaCardBackPreview;
  String? _businessCertificatePreview;
  final List<String> _businessDocumentsPreview = [];
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  List<String> _filteredAreas = [];
  String? _selectedRegion;
  String? _selectedArea;
  final _formKey = GlobalKey<FormState>();
  final _shopNameController = TextEditingController();
  final _shopDescriptionController = TextEditingController();
  final _businessPhoneController = TextEditingController();
  final _businessEmailController = TextEditingController();
  final _businessLocationController = TextEditingController();
  final _ghanaCardController = TextEditingController();
  String? _selectedBusinessType;
  String? _businessCertificateUrl;
  String? _ghanaCardFrontUrl;
  String? _ghanaCardBackUrl;
  final List<String> _businessDocumentUrls = [];
  bool isLoading = false;
  String? errorMessage;

  bool isLoadingGhanaCardFront = false;
  bool isLoadingGhanaCardBack = false;
  bool isLoadingBusinessCertificate = false;
  bool isLoadingBusinessDocuments = false;

  // Business type options
  final List<String> _businessTypes = [
    'Supplier',
    'Distributor',
    'Manufacturer',
    'Farmer',
    'Wholesaler',
    'Retailer',
    'Factory',
    'Warehouse',
    'Other'
  ];

  final Rx<String> selectedCountryCode = '+233'.obs;
  final Rx<String> selectedCountryFlag = '🇬🇭'.obs;

  void _filterAreas(String query) {
    if (_selectedRegion == null) {
      setState(() {
        _filteredAreas = [];
      });
      return;
    }

    final areas = GhanaRegions.regionAreas[_selectedRegion!] ?? [];
    setState(() {
      _searchQuery = query;
      _filteredAreas = areas
          .where((area) => area.toLowerCase().contains(query.toLowerCase()))
          .toList();
    });
  }

  void _showCustomAreaDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        String customArea = '';
        return AlertDialog(
          title: const Text('Enter Custom Area'),
          content: TextFormField(
            decoration: const InputDecoration(
              labelText: 'Area Name',
              border: OutlineInputBorder(),
            ),
            onChanged: (value) => customArea = value,
          ),
          actions: [
            TextButton(
              onPressed: () => context.pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                if (customArea.isNotEmpty) {
                  setState(() {
                    _selectedArea = customArea;
                    _businessLocationController.text =
                        '${_selectedRegion ?? ''}, $customArea';
                  });
                  context.pop();
                }
              },
              child: const Text('Save'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _pickBusinessCertificate() async {
    try {
      final picker = ImagePicker();
      final XFile? pickedFile = await picker.pickImage(
        source: ImageSource.gallery,
      );

      if (pickedFile != null) {
        if (!mounted) return;

        setState(() {
          isLoadingBusinessCertificate = true;
          errorMessage = null;
        });

        // Create a unique path for the certificate
        final path =
            'business_certificates/${DateTime.now().millisecondsSinceEpoch}';
        final storageRef = FirebaseStorage.instance.ref().child(path);

        // Handle upload based on platform
        UploadTask uploadTask;
        if (kIsWeb) {
          // For web, use bytes
          final bytes = await pickedFile.readAsBytes();
          uploadTask = storageRef.putData(
            bytes,
            SettableMetadata(contentType: 'image/jpeg'),
          );
        } else {
          // For mobile, use File
          uploadTask = storageRef.putFile(File(pickedFile.path));
        }

        // Wait for upload to complete
        await uploadTask;

        if (uploadTask.snapshot.state == TaskState.success) {
          final downloadUrl = await storageRef.getDownloadURL();

          if (!mounted) return;

          setState(() {
            _businessCertificateUrl = downloadUrl;
            _businessCertificatePreview = downloadUrl;
            isLoadingBusinessCertificate = false;
          });

          if (mounted) {
            _showMessage(
              context,
              'Business certificate uploaded successfully',
              true,
            );
          }
        } else {
          throw 'Upload failed: ${uploadTask.snapshot.state}';
        }
      }
    } catch (e) {
      // Add debug print

      if (!mounted) return;

      setState(() {
        isLoadingBusinessCertificate = false;
        errorMessage = 'Failed to upload certificate: $e';
      });

      if (mounted) {
        _showMessage(
          context,
          errorMessage ?? 'An error occurred',
          false,
        );
      }
    }
  }

  Future<void> _pickGhanaCardImage(bool isFront) async {
    final downloadUrl = await DocumentUploadService.uploadGhanaCard(
      isFront: isFront,
      setLoading: (loading) {
        if (mounted) {
          setState(() => isFront
              ? isLoadingGhanaCardFront = loading
              : isLoadingGhanaCardBack = loading);
        }
      },
      setError: (error) {
        if (mounted) setState(() => errorMessage = error);
      },
      showMessage: (message, success) {
        if (mounted) {
          _showMessage(context, message, success);
        }
      },
    );

    if (downloadUrl.isNotEmpty && mounted) {
      setState(() {
        if (isFront) {
          _ghanaCardFrontUrl = downloadUrl;
        } else {
          _ghanaCardBackUrl = downloadUrl;
        }
      });
    }
  }

  // Add these to your state variables
  final List<UploadedFile> _uploadedDocuments = [];

  Future<void> _pickBusinessDocument() async {
    try {
      final uploadedFile = await DocumentUploadService.uploadBusinessDocument(
        setLoading: (loading) {
          if (mounted) {
            setState(() => isLoadingBusinessDocuments = loading);
          }
        },
        setError: (error) {
          if (mounted) {
            setState(() => errorMessage = error);
          }
        },
        showMessage: (message, success) {
          if (mounted) {
            _showMessage(context, message, success);
          }
        },
      );

      if (mounted) {
        setState(() {
          _uploadedDocuments.add(uploadedFile);
          _businessDocumentUrls.add(uploadedFile.url);
        });
      }
    } catch (e) {
      // Error already handled in service
    }
  }

  // Helper method to show profile completion dialog
  void _showProfileCompletionDialog(String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Profile Incomplete'),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => context.pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                context.pop(); // Close dialog
                context.go('/profile'); // Navigate to profile screen
              },
              child: const Text('Go to Profile'),
            ),
          ],
        );
      },
    );
  }

  // Update the handleCreateShop method to use the dialog
  Future<void> _handleCreateShop() async {
    try {
      if (!_formKey.currentState!.validate()) return;

      // Remove this validation block:
      // if (_ghanaCardFrontUrl == null || _ghanaCardBackUrl == null) {
      //   _showMessage(
      //       context, 'Please upload both sides of your Ghana Card', false);
      //   return;
      // }

      setState(() {
        isLoading = true;
        errorMessage = null;
      });

      // Get current user
      final user = AuthenticationRepository.instance.firebaseUser;
      if (user == null) {
        _showMessage(context, 'Please login to create a shop', false);
        return;
      }

      // Fetch user profile with debug prints

      final userDoc = await FirebaseFirestore.instance
          .collection('Users') // Changed from 'users' to 'Users'
          .doc(user.uid)
          .get();

      if (!userDoc.exists) {
        _showMessage(context, 'Please complete your profile first', false);
        context.go('/profile'); // Added navigation to profile
        return;
      }

      final userData = userDoc.data()!;

      // Check FirstName and LastName (using proper case)
      final firstName = userData['FirstName']?.toString().trim() ?? '';
      final lastName = userData['LastName']?.toString().trim() ?? '';

      if (firstName.isEmpty) {
        _showMessage(
          context,
          'Please complete your profile by adding your first name',
          false,
        );
        context.go('/profile');
        return;
      }

      if (lastName.isEmpty) {
        _showMessage(
          context,
          'Please complete your profile by adding your last name',
          false,
        );
        context.go('/profile');
        return;
      }

      // If all checks pass, continue with payment and shop creation
      final success = await PaystackService.makePayment(
        email: _businessEmailController.text.trim(),
        amount: _listingFee,
        reference: 'shop_reg_${DateTime.now().millisecondsSinceEpoch}',
        context: context,
        onPaymentComplete: (paymentSuccess) async {
          if (paymentSuccess) {
            // Create shop model with user profile data
            final shop = ShopModel(
              id: '',
              name: _shopNameController.text.trim(),
              description: _shopDescriptionController.text.trim(),
              phone: _businessPhoneController.text.trim(),
              email: _businessEmailController.text.trim(),
              location: _businessLocationController.text.trim(),
              businessType: _selectedBusinessType ?? 'Retailer',
              ghanaCardFront: _ghanaCardFrontUrl ?? '', // Handle null case
              ghanaCardBack: _ghanaCardBackUrl ?? '', // Handle null case
              businessDocuments: _businessDocumentUrls,
              businessCertificate: _businessCertificateUrl ?? '',
              ghanaCardNumber: _ghanaCardController.text.trim(),
              ownerId: user.uid,
              ownerFirstName: firstName,
              ownerLastName: lastName,
              ownerEmail: userData['Email'] ?? '', // Get from user profile
              ownerPhone:
                  userData['PhoneNumber'] ?? '', // Get from user profile
              createdAt: DateTime.now(),
              status: 'pending',
              shopLocation: ShopLocation(
                region: _selectedRegion ?? '',
                area: _selectedArea ?? '',
                exactLocation: _businessLocationController.text.trim(),
              ),
              listingFee: _listingFee,
              isPaid: true,
              paidAt: DateTime.now(),
              bankAccountName: _bankAccountNameController.text.trim(),
              bankAccountNumber: _bankAccountNumberController.text.trim(),
              bankName: _selectedBank, // Make sure this is included
              mobileMoneyNumber: _mobileMoneyNumberController.text.trim(),
              mobileMoneyName: _mobileMoneyNameController.text.trim(),
              mobileMoneyProvider: _selectedMobileMoneyProvider,
            );

            // Log the shop data before saving (for debugging)
            debugPrint('Bank Name: $_selectedBank');
            debugPrint('Shop Data: ${shop.toJson()}');
            debugPrint(
                'Calling _shopController.createShop with ownerId: ${shop.ownerId}'); // ADD THIS LINE

            final shopCreated = await _shopController.createShop(shop);

            if (shopCreated) {
              if (mounted) {
                _showMessage(context, 'Shop created successfully!', true);
                final shopId = _shopController.currentShop.value?.id;
                if (shopId != null) {
                  context.go('/shops/$shopId/manage');
                }
              }
            } else {
              if (mounted) {
                _showMessage(
                  context,
                  'Failed to create shop: ${_shopController.error.value}',
                  false,
                );
              }
            }
          }
        },
      );

      if (!success && mounted) {
        _showMessage(context, 'Payment failed', false);
      }
    } catch (e) {
      if (mounted) {
        _showMessage(context, 'Error: $e', false);
      }
    } finally {
      if (mounted) {
        setState(() => isLoading = false);
      }
    }
  }

  // Update the country code dialog method
  void _showCountryCodeDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          child: ListView(
            shrinkWrap: true,
            children: [
              ListTile(
                leading: const Text('🇬🇭', style: TextStyle(fontSize: 24)),
                title: const Text('Ghana'),
                trailing: const Text('+233'),
                onTap: () {
                  setState(() => _selectedCountryCode = '+233');
                  context.pop();
                },
              ),
              // Add more countries as needed
            ],
          ),
        );
      },
    );
  }

  // Update the bank changed callback
  void _onBankChanged(String? value) {
    setState(() {
      _selectedBank = value;
      debugPrint('Selected Bank: $_selectedBank'); // Debug log
    });
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 600;

    return CreateShopForm(
      isFirebaseInitialized: _isFirebaseInitialized,
      formKey: _formKey,
      isMobile: isMobile,
      isLoading: isLoading,
      isLoadingFee: _isLoadingFee,
      listingFee: _listingFee,
      shopNameController: _shopNameController,
      shopDescriptionController: _shopDescriptionController,
      businessPhoneController: _businessPhoneController,
      businessEmailController: _businessEmailController,
      businessLocationController: _businessLocationController,
      ghanaCardController: _ghanaCardController,
      bankAccountNameController: _bankAccountNameController,
      bankAccountNumberController: _bankAccountNumberController,
      mobileMoneyNumberController: _mobileMoneyNumberController,
      mobileMoneyNameController: _mobileMoneyNameController,
      selectedBusinessType: _selectedBusinessType,
      selectedRegion: _selectedRegion,
      selectedArea: _selectedArea,
      selectedBank: _selectedBank,
      selectedMobileMoneyProvider: _selectedMobileMoneyProvider,
      businessTypes: _businessTypes,
      ghanaianBanks: _ghanaianBanks,
      mobileMoneyProviders: _mobileMoneyProviders,
      filteredAreas: _filteredAreas,
      ghanaCardFrontUrl: _ghanaCardFrontUrl,
      ghanaCardBackUrl: _ghanaCardBackUrl,
      businessCertificateUrl: _businessCertificateUrl,
      businessDocumentUrls: _businessDocumentUrls,
      businessDocumentsPreview: _businessDocumentsPreview,
      isLoadingGhanaCardFront: isLoadingGhanaCardFront,
      isLoadingGhanaCardBack: isLoadingGhanaCardBack,
      isLoadingBusinessCertificate: isLoadingBusinessCertificate,
      isLoadingBusinessDocuments: isLoadingBusinessDocuments,
      onBusinessTypeChanged: (value) =>
          setState(() => _selectedBusinessType = value),
      onRegionChanged: (value) {
        setState(() {
          _selectedRegion = value;
          _selectedArea = null;
          _businessLocationController.text = '';
          _filterAreas('');
        });
      },
      onAreaChanged: (value) {
        setState(() {
          _selectedArea = value;
        });
      },
      onBankChanged: _onBankChanged,
      onMobileMoneyProviderChanged: (value) =>
          setState(() => _selectedMobileMoneyProvider = value),
      onCreateShopPressed: _handleCreateShop,
      onPickGhanaCardImage: (bool isFront) => _pickGhanaCardImage(isFront),
      onPickBusinessCertificate: _pickBusinessCertificate,
      onPickBusinessDocument: _pickBusinessDocument,
      selectedCountryCode: _selectedCountryCode,
      onCountryCodeTap: () => _showCountryCodeDialog(context),
    );
  }

// Create a reusable method for showing centered messages
  void _showMessage(BuildContext context, String message, bool isSuccess) {
    // Get screen dimensions
    final size = MediaQuery.of(context).size;
    final screenWidth = size.width;
    final screenHeight = size.height;

    // Dismiss any existing SnackBars
    ScaffoldMessenger.of(context).removeCurrentSnackBar();

    final snackBar = SnackBar(
      content: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                isSuccess ? Icons.check_circle : Icons.error,
                color: Colors.white,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    isSuccess ? 'Success' : 'Error',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    message,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      backgroundColor: isSuccess ? Colors.green.shade600 : Colors.red.shade600,
      behavior: SnackBarBehavior.floating,
      margin: EdgeInsets.only(
        bottom: screenHeight * 0.4,
        left: screenWidth > 800 ? screenWidth * 0.3 : 20,
        right: screenWidth > 800 ? screenWidth * 0.3 : 20,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      duration: Duration(seconds: isSuccess ? 3 : 5),
      dismissDirection: DismissDirection.horizontal,
      elevation: 8,
    );

    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }
}
