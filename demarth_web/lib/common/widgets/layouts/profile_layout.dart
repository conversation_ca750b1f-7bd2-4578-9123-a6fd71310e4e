import 'package:demarth/common/widgets/appbar/mobile_appbar.dart';
import 'package:flutter/material.dart';
import '../custom_shapes/containers/web_navbar.dart';
import '../navigation/profile_sidebar.dart';
import 'base_layout.dart';

class ProfileLayout extends StatelessWidget {
  final String currentRoute;
  final Widget child;
  final bool showSidebarOnMobile;

  const ProfileLayout({
    super.key,
    required this.currentRoute,
    required this.child,
    this.showSidebarOnMobile = false,
    this.showBackArrow = false,
    this.title,
  });

  final bool showBackArrow;
  final String? title;

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 600;

    return Scaffold(
      appBar: isMobile
          ? TMobileAppBar(
              preferredSize: const Size.fromHeight(kToolbarHeight),
              showSearchBar: true,
              showBackArrow: showBackArrow,
              title: title != null ? Text(title!) : null,
            )
          : null,
      body: TBaseLayout(
        appBar: !isMobile ? const TWebNavBar() : null,
        child: Center(
          child: SizedBox(
            width: screenWidth * 0.9, // Take 90% of screen width
            child: Row(
              crossAxisAlignment:
                  CrossAxisAlignment.start, // Align items at the top
              children: [
                if (!isMobile)
                  ProfileSidebar(
                    currentRoute: currentRoute,
                  ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(
                        top: 20, left: 20), // Add padding to content area
                    child: child,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
