import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:iconsax/iconsax.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../utils/constants/colors.dart';

class ProfileSidebar extends StatelessWidget {
  final String currentRoute;

  const ProfileSidebar({
    super.key,
    required this.currentRoute,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 0,
      margin: const EdgeInsets.only(top: 20),
      color: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: const BorderRadius.only(
          topRight: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
        side: BorderSide(
          color: Colors.grey.shade200,
          width: 1,
        ),
      ),
      child: Container(
        width: 280,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Modern Header Section
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    TColors.primary.withValues(alpha: 0.1),
                    TColors.primary.withValues(alpha: 0.05),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: TColors.primary,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: TColors.primary.withValues(alpha: 0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: const Icon(
                      Iconsax.user,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'My Account',
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.w700,
                                  color: const Color(0xFF1A1D29),
                                ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        'Manage your profile',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey.shade600,
                              fontSize: 12,
                            ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Navigation Section Header
            Padding(
              padding: const EdgeInsets.only(left: 4, bottom: 12),
              child: Text(
                'NAVIGATION',
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                      color: Colors.grey.shade500,
                      fontWeight: FontWeight.w600,
                      letterSpacing: 1.2,
                    ),
              ),
            ),

            // Profile Navigation Items
            _SidebarItem(
              icon: Iconsax.user,
              title: 'Profile',
              isActive: currentRoute == '/profile',
              onTap: () => context.go('/profile'),
            ),
            _SidebarItem(
              icon: Iconsax.bag_tick,
              title: 'My Orders',
              isActive: currentRoute == '/orders',
              onTap: () => context.go('/orders'),
            ),
            _SidebarItem(
              icon: Iconsax.discount_shape,
              title: 'My Coupons',
              isActive: currentRoute == '/coupons',
              onTap: () => context.go('/coupons'),
            ),
            _SidebarItem(
              icon: Iconsax.message,
              title: 'Messages',
              isActive: currentRoute == '/messages',
              onTap: () => context.go('/messages'),
            ),
            _SidebarItem(
              icon: Iconsax.safe_home,
              title: 'My Addresses',
              isActive: currentRoute == '/address',
              onTap: () => context.go('/address'),
            ),
            _SidebarItem(
              icon: Iconsax.security_card,
              title: 'Account Privacy',
              isActive: currentRoute == '/account-privacy',
              onTap: () => context.go('/account-privacy'),
            ),

            const SizedBox(height: 20),

            // Business Section Header
            Padding(
              padding: const EdgeInsets.only(left: 4, bottom: 12),
              child: Text(
                'BUSINESS',
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                      color: Colors.grey.shade500,
                      fontWeight: FontWeight.w600,
                      letterSpacing: 1.2,
                    ),
              ),
            ),

            // Live Chat option
            // _SidebarItem(
            //   icon: Iconsax.message_question,
            //   title: 'Live Chat',
            //   isActive: false,
            //   onTap: () {
            //     ScaffoldMessenger.of(context).showSnackBar(
            //       const SnackBar(
            //         content: Text('Live chat feature will be available soon!'),
            //         backgroundColor: TColors.primary,
            //       ),
            //     );
            //   },
            // ),

            // Sellers Account Signup option
            StreamBuilder<QuerySnapshot>(
              stream: FirebaseFirestore.instance
                  .collection('Shops')
                  .where('ownerId',
                      isEqualTo: FirebaseAuth.instance.currentUser?.uid)
                  .limit(1)
                  .snapshots(),
              builder: (context, snapshot) {
                if (!snapshot.hasData) {
                  return const SizedBox.shrink();
                }

                final hasShop = snapshot.data!.docs.isNotEmpty;
                final shopId = hasShop ? snapshot.data!.docs.first.id : null;

                return _SidebarItem(
                  icon: Iconsax.shop,
                  title: hasShop ? 'Shop Dashboard' : 'Become a Seller',
                  isActive: hasShop
                      ? currentRoute.startsWith('/shops/') &&
                          currentRoute.endsWith('/manage')
                      : currentRoute == '/shops/create',
                  onTap: () => hasShop
                      ? context.go('/shops/$shopId/manage')
                      : context.go('/shops/create'),
                );
              },
            ),

            const SizedBox(height: 20),

            // Logout Section
            Container(
              margin: const EdgeInsets.only(top: 8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.red.shade100,
                  width: 1,
                ),
              ),
              child: _SidebarItem(
                icon: Iconsax.logout,
                title: 'Logout',
                isActive: false,
                isLogout: true,
                onTap: () async {
                  await FirebaseAuth.instance.signOut();
                  if (context.mounted) {
                    context.go('/');
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _SidebarItem extends StatefulWidget {
  final IconData icon;
  final String title;
  final bool isActive;
  final bool isLogout;
  final VoidCallback onTap;

  const _SidebarItem({
    required this.icon,
    required this.title,
    required this.isActive,
    required this.onTap,
    this.isLogout = false,
  });

  @override
  State<_SidebarItem> createState() => _SidebarItemState();
}

class _SidebarItemState extends State<_SidebarItem> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    final primaryColor = widget.isLogout ? Colors.red : TColors.primary;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: widget.onTap,
        onHover: (value) {
          setState(() {
            isHovered = value;
          });
        },
        borderRadius: BorderRadius.circular(12),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          margin: const EdgeInsets.symmetric(vertical: 2),
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
          decoration: BoxDecoration(
            color: widget.isActive
                ? primaryColor.withValues(alpha: 0.1)
                : isHovered
                    ? primaryColor.withValues(alpha: 0.05)
                    : Colors.transparent,
            borderRadius: BorderRadius.circular(12),
            border: widget.isActive
                ? Border.all(
                    color: primaryColor.withValues(alpha: 0.2),
                    width: 1,
                  )
                : null,
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: widget.isActive
                      ? primaryColor.withValues(alpha: 0.15)
                      : primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  widget.icon,
                  size: 18,
                  color: widget.isActive
                      ? primaryColor
                      : primaryColor.withValues(alpha: 0.8),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  widget.title,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: widget.isActive
                            ? primaryColor
                            : widget.isLogout
                                ? Colors.red.shade700
                                : const Color(0xFF1A1D29),
                        fontWeight:
                            widget.isActive ? FontWeight.w600 : FontWeight.w500,
                      ),
                ),
              ),
              if (widget.isActive)
                Container(
                  width: 6,
                  height: 6,
                  decoration: BoxDecoration(
                    color: primaryColor,
                    shape: BoxShape.circle,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
